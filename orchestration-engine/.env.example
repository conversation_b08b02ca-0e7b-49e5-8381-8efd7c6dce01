# Application settings
APP_NAME="Orchestration Engine"
APP_ENV="Production"
APP_DEBUG="False"

#Commnication ports
KAFKA_BOOTSTRAP_SERVERS="KAFKA_BOOTSTRAP_SERVERS(with port)"

# MCP Execution Topics
KAFKA_MCP_EXECUTION_REQUEST_TOPIC=
KAFKA_MCP_EXECUTION_RESULT_TOPIC=

# Node Execution Topics
KAFKA_NODE_EXECUTION_REQUEST_TOPIC=
KAFKA_NODE_EXECUTION_RESULT_TOPIC=

# Agent Execution Topics
KAFKA_AGENT_EXECUTION_REQUEST_TOPIC=
KAFKA_AGENT_EXECUTION_RESULT_TOPIC=

# Workflow Executor Topics
KAFKA_WORKFLOW_EXECUTION_REQUEST_TOPIC=
KAFKA_WORKFLOW_EXECUTION_RESULT_TOPIC=

# Main Kafka Consumer Group
GROUP_ID=

# Main Workflow Entry Topics
KAFKA_WORKFLOW_REQUEST_TOPIC=
KAFKA_EXECUTION_REQUEST_TOPIC=
KAFKA_APPROVAL_REQUEST_TOPIC=

# Redis settings
REDIS_HOST="REDIS_HOST"
REDIS_PORT="REDIS_PORT"
REDIS_RESULTS_DB_INDEX=0
REDIS_STATE_DB_INDEX=1
REDIS_PASSWORD="REDIS_PASSWORD"
# TTL in seconds for Redis keys (default: 5 minutes for results, 10 minutes for state)
REDIS_RESULTS_TTL=300
REDIS_STATE_TTL=600

# PostgreSQL settings
DB_HOST="localhost"
DB_PORT=5432
DB_USER="postgres"
DB_PASSWORD="your_password"
DB_NAME="orchestration_engine"

# GCS connection details
GCS_CRED="GCS_CRED"
BUCKET_NAME="BUCKET_NAME"

# API Gateway Authentication
ORCHESTRATION_SERVER_AUTH_KEY= "ORCHESTRATION_SERVER_AUTH_KEY"
API_GATEWAY_URL="API_GATEWAY_URL"