import json
import logging

# Create logger for this module
logger = logging.getLogger(__name__)


def _has_handle_mapping_for_field(field_name, transition, workflow):
    """
    Check if a field has a corresponding handle mapping defined in the workflow.

    Args:
        field_name: The name of the field to check
        transition: The transition containing the field
        workflow: The workflow containing handle mapping information

    Returns:
        bool: True if the field has a handle mapping, False otherwise
    """
    # Get input_data configurations from the transition
    input_data_configs = transition.get("node_info", {}).get("input_data", [])

    # Check each input_data configuration for handle mappings
    for input_config in input_data_configs:
        handle_mappings = input_config.get("handle_mappings", [])

        # Check if any handle mapping targets this field
        for mapping in handle_mappings:
            target_handle_id = mapping.get("target_handle_id")
            if target_handle_id == field_name:
                return True

    return False


def _parse_json_if_needed(value, field_name, transition, workflow):
    """
    Parse JSON string if the field is expected to be an object type.

    Args:
        value: The field value to potentially parse
        field_name: The name of the field
        transition: The transition containing the field
        workflow: The workflow containing schema information

    Returns:
        Parsed value if it was a JSON string for an object field, otherwise original value
    """
    # Only process string values that look like JSON
    if not isinstance(value, str) or not (
        value.strip().startswith("{") or value.strip().startswith("[")
    ):
        return value

    # Get the node_id to find the corresponding input schema
    node_id = transition.get("node_info", {}).get("node_id")

    # Find the node in the nodes array
    for node in workflow.get("nodes", []):
        if node.get("id") == node_id:
            # Check each server tool's input schema
            for server_tool in node.get("server_tools", []):
                # Look for the field in predefined_fields
                for field in server_tool.get("input_schema", {}).get(
                    "predefined_fields", []
                ):
                    if field.get("field_name") == field_name:
                        # Check if the field type is object
                        field_data_type = field.get("data_type", {})
                        if isinstance(field_data_type, dict):
                            field_type = field_data_type.get("type", "")
                        else:
                            field_type = str(field_data_type)

                        # If it's an object or array type, try to parse the JSON
                        if field_type in ["object", "array"]:
                            try:
                                parsed_value = json.loads(value)
                                logger.debug(
                                    f"Parsed JSON for field {field_name}: {value} -> {parsed_value}"
                                )
                                return parsed_value
                            except (json.JSONDecodeError, ValueError) as e:
                                logger.debug(
                                    f"Failed to parse JSON for field {field_name}: {e}"
                                )
                                # Return original value if parsing fails
                                return value

    # Return original value if field not found or not object type
    return value


def _inject_user_input_for_conditional_components(
    transition, transition_id, user_payload_template
):
    """
    Inject user input into global_context for conditional components.

    For conditional components, user input should be available in the global_context
    parameter during execution, not in the regular tool parameters.

    Args:
        transition: The transition to update
        transition_id: The ID of the transition
        user_payload_template: The user payload template containing user input values
    """
    # Check if this is a conditional component
    node_type = transition.get("node_info", {}).get("node_type", "")
    is_conditional = node_type == "conditional" or "ConditionalNode" in transition_id

    logger.debug(
        f"Checking transition {transition_id}: node_type='{node_type}', is_conditional={is_conditional}"
    )

    if not is_conditional:
        logger.debug(f"Skipping non-conditional transition {transition_id}")
        return

    logger.debug(f"Processing conditional component {transition_id}")

    # Find or create the tool_params for this conditional component
    tools_to_use = transition.get("node_info", {}).get("tools_to_use", [])

    logger.debug(
        f"Found {len(tools_to_use)} tools_to_use for transition {transition_id}"
    )

    if not tools_to_use:
        logger.debug(f"No tools_to_use found for conditional component {transition_id}")
        return

    # Get the first tool (conditional components typically have only one tool)
    tool = tools_to_use[0]
    tool_params = tool.get("tool_params", {})

    logger.debug(f"Tool structure for {transition_id}: {tool}")
    logger.debug(f"Tool params for {transition_id}: {tool_params}")

    # Get or create the global_context parameter in the items array
    items = tool_params.setdefault("items", [])
    global_context_param = None

    # Look for existing global_context parameter
    for item in items:
        if item.get("field_name") == "global_context":
            global_context_param = item
            break

    # Create global_context parameter if it doesn't exist
    if not global_context_param:
        logger.debug(f"Creating new global_context parameter for {transition_id}")
        global_context_param = {
            "field_name": "global_context",
            "data_type": "object",
            "field_value": {},
        }
        items.append(global_context_param)
    else:
        logger.debug(f"Found existing global_context parameter for {transition_id}")

    # Get the current global_context value
    global_context = global_context_param.get("field_value", {})
    logger.debug(f"Current global_context for {transition_id}: {global_context}")

    # Process each user input field - handle both object and array formats
    if isinstance(user_payload_template, list):
        # Array format - iterate through entries
        for entry in user_payload_template:
            if isinstance(entry, dict):
                for field_key, payload_value in entry.items():
                    logger.debug(
                        f"Processing user input field '{field_key}' with value: {payload_value}"
                    )
    else:
        # Object format - iterate through key-value pairs
        if isinstance(user_payload_template, dict):
            for field_name, payload_value in user_payload_template.items():
                logger.debug(
                    f"Processing user input field '{field_name}' with value: {payload_value}"
                )

        if isinstance(payload_value, dict) and "value" in payload_value:
            # Handle complex payload structure with transition_id
            value = payload_value["value"]
            target_transition_id = payload_value.get("transition_id", "")

            logger.debug(
                f"Complex payload - value: {value}, target_transition: {target_transition_id}"
            )

            # Check if this input is intended for the current transition
            if target_transition_id and target_transition_id in transition_id:
                logger.debug(
                    f"User input '{field_name}' is intended for transition {transition_id}"
                )

                global_context[field_name] = value
                logger.debug(
                    f"Injected user input '{field_name}' = '{value}' into global_context for conditional component '{transition_id}'"
                )
            else:
                logger.debug(
                    f"Skipping user input '{field_name}' - not intended for transition {transition_id} (target: {target_transition_id})"
                )
        else:
            # Handle simple value structure
            global_context[field_name] = payload_value
            logger.debug(
                f"Injected simple user input '{field_name}' = '{payload_value}' into global_context for conditional component '{transition_id}'"
            )

    # Update the global_context parameter with the injected user input
    global_context_param["field_value"] = global_context
    logger.debug(f"Final global_context for {transition_id}: {global_context}")

    # Update the tool_params in the transition
    tool["tool_params"] = tool_params


def _update_loop_config_from_tool_params(transition):
    """
    Update loop_config from tool_params for loop transitions.

    This bridges the gap between user input (stored in tool_params) and
    loop execution (which reads from loop_config).

    Args:
        transition: The transition dictionary containing both tool_params and loop_config
    """
    # Check if this is a loop transition
    if transition.get("execution_type") != "loop":
        return

    # Get tool_params
    tools_to_use = transition.get("node_info", {}).get("tools_to_use", [])
    if not tools_to_use:
        return

    tool_params = tools_to_use[0].get("tool_params", {})
    tool_params_items = tool_params.get("items", [])

    # Extract values from tool_params and intelligently map them to loop configuration
    # Look for any field that contains array/list data for iteration_list
    # Look for any field that contains numeric data for range parameters

    iteration_list_value = None
    start_value = None
    end_value = None
    step_value = None
    batch_size_value = 1
    source_type_value = None

    for item in tool_params_items:
        field_name = item.get("field_name", "")
        field_value = item.get("field_value")
        data_type = item.get("data_type")

        # If it's a JSON string that should be parsed, parse it
        if (
            field_name
            and isinstance(field_value, str)
            and data_type in ["array", "object"]
        ):
            try:
                field_value = json.loads(field_value)
            except (json.JSONDecodeError, ValueError):
                pass  # Keep original value if parsing fails

        # Extract source_type to determine iteration approach
        if "source_type" in field_name.lower():
            source_type_value = field_value

        if field_value is not None:
            # Check if this looks like an iteration list (array/list)
            if isinstance(field_value, list):
                iteration_list_value = field_value

            # Check if this looks like range parameters based on field name patterns
            elif "start" in field_name.lower():
                start_value = field_value
            elif "end" in field_name.lower():
                end_value = field_value
            elif "step" in field_name.lower():
                step_value = field_value
            elif "batch" in field_name.lower():
                try:
                    batch_size_value = (
                        int(field_value)
                        if isinstance(field_value, str)
                        else field_value
                    )
                except (ValueError, TypeError):
                    batch_size_value = 1

    # Check if iteration_list is connected via handle mapping
    input_data_configs = transition.get("node_info", {}).get("input_data", [])
    has_iteration_list_mapping = False
    for input_config in input_data_configs:
        handle_mappings = input_config.get("handle_mappings", [])
        for mapping in handle_mappings:
            if mapping.get("target_handle_id") == "iteration_list":
                has_iteration_list_mapping = True
                break
        if has_iteration_list_mapping:
            break

    # Get or create loop_config
    loop_config = transition.get("loop_config", {})

    # Determine iteration source type and create proper structure
    # Priority: 1) Handle mapping, 2) source_type setting, 3) Explicit iteration_list, 4) Number range
    if has_iteration_list_mapping or source_type_value == "iteration_list":
        # Use iteration_list approach - clean up conflicting number_range parameters
        loop_config["iteration_source"] = {
            "iteration_list": (
                iteration_list_value if iteration_list_value is not None else []
            ),
            "batch_size": batch_size_value,
        }

        # Remove conflicting number_range parameters from tool_params
        _clean_conflicting_tool_params(tool_params, keep_type="iteration_list")

    elif start_value is not None and end_value is not None:
        # Use number_range approach
        start = start_value
        end = end_value
        step = step_value if step_value is not None else 1

        # Convert to integers if they're strings
        try:
            start = int(start) if isinstance(start, str) else start
            end = int(end) if isinstance(end, str) else end
            step = int(step) if isinstance(step, str) else step
        except (ValueError, TypeError):
            start, end, step = 1, 10, 1

        # Create proper iteration_source structure for number_range
        loop_config["iteration_source"] = {
            "number_range": {"start": start, "end": end, "step": step}
        }

        # Remove conflicting iteration_list parameters from tool_params
        _clean_conflicting_tool_params(tool_params, keep_type="number_range")

    # Update the transition with the modified loop_config
    transition["loop_config"] = loop_config


def _clean_conflicting_tool_params(tool_params, keep_type):
    """
    Remove conflicting parameters from tool_params based on the iteration source type.

    Args:
        tool_params: The tool_params dictionary to clean
        keep_type: Either "iteration_list" or "number_range" - determines which params to keep
    """
    items = tool_params.get("items", [])

    if keep_type == "iteration_list":
        # Remove number_range related parameters
        conflicting_fields = ["start", "end", "step"]
    else:  # keep_type == "number_range"
        # Remove iteration_list related parameters
        conflicting_fields = ["iteration_list"]

    # Filter out conflicting parameters
    filtered_items = []
    for item in items:
        field_name = item.get("field_name", "").lower()
        if not any(conflict in field_name for conflict in conflicting_fields):
            filtered_items.append(item)

    tool_params["items"] = filtered_items


def initialize_workflow_with_params(workflow, params):
    """
    Updates the workflow structure by replacing field values based on user-dependent fields and handling None values with proper validation.
    Also initializes global context definition values from the payload.

    Args:
        workflow (dict): The original workflow dictionary.
        params (dict): The parameters containing user-dependent fields, values, and global context definitions.

    Returns:
        dict: The updated workflow with parameter values and global context values inserted.

    Raises:
        ValueError: If a required field is None and the user hasn't provided a value
                   or if a required global context variable is None and not provided in payload
    """

    user_dependent_fields = params["payload"]["user_dependent_fields"]
    user_payload_template = params["payload"]["user_payload_template"]

    # Handle global_context_defs for both array and object formats
    global_context_values = {}
    if isinstance(user_payload_template, dict):
        # Object format - get global_context_defs directly
        global_context_values = user_payload_template.get("global_context_defs", {})
    # Array format doesn't support global_context_defs (they would be in individual entries)

    # Create a mapping of transition IDs to transitions for quick lookup
    transitions_by_id = {
        transition.get("id"): transition
        for transition in workflow.get("transitions", [])
    }

    # Process tool parameters
    for transition in workflow.get("transitions", []):
        transition_id = transition.get("id")
        # Handle tool parameters
        for tool in transition.get("node_info", {}).get("tools_to_use", []):
            for item in tool.get("tool_params", {}).get("items", []):
                field_name = item.get("field_name")
                field_value = item.get("field_value")

                # Check if this field has a value with a specific transition ID
                field_updated = False

                # Check for field name match - support both object and array formats
                payload_value = None

                if isinstance(user_payload_template, list):
                    # Array format - find matching field by transition_id
                    for entry in user_payload_template:
                        if isinstance(entry, dict) and field_name in entry:
                            candidate_value = entry[field_name]
                            if (isinstance(candidate_value, dict) and
                                candidate_value.get("transition_id") == transition_id):
                                payload_value = candidate_value
                                break
                else:
                    # Object format - direct field name match
                    if isinstance(user_payload_template, dict) and field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]

                if payload_value:

                    # Check if the payload value is a dictionary with transition_id
                    if (
                        isinstance(payload_value, dict)
                        and "value" in payload_value
                        and "transition_id" in payload_value
                    ):
                        # If this is the specified transition, update the field
                        if payload_value["transition_id"] == transition_id:
                            extracted_value = payload_value["value"]

                            # Check if this is a JSON string that should be parsed for object fields
                            parsed_value = _parse_json_if_needed(
                                extracted_value, field_name, transition, workflow
                            )

                            item["field_value"] = parsed_value
                            field_updated = True
                            logger.debug(
                                f"Applied transition-specific value for field '{field_name}' to transition '{transition_id}'"
                            )
                        else:
                            logger.debug(
                                f"Skipping field '{field_name}' for transition '{transition_id}' (intended for '{payload_value['transition_id']}')"
                            )
                        # If transition_id doesn't match, don't apply this value to current transition
                        # The value is intended for a different transition, so skip it
                    else:
                        # For simple values (not transition-specific), update as before
                        # Check if this is a JSON string that should be parsed for object fields
                        parsed_value = _parse_json_if_needed(
                            payload_value, field_name, transition, workflow
                        )

                        item["field_value"] = parsed_value
                        field_updated = True

                # Handle user-dependent fields that weren't updated by transition ID
                if not field_updated and field_name in user_dependent_fields:
                    logger.debug(
                        f"Processing user-dependent field '{field_name}' for transition '{transition_id}'"
                    )
                    # Try to find a value for this field that might be in a different format
                    payload_value = None
                    if isinstance(user_payload_template, dict) and field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]
                    elif isinstance(user_payload_template, list):
                        # Array format - find any matching field (fallback)
                        for entry in user_payload_template:
                            if isinstance(entry, dict) and field_name in entry:
                                payload_value = entry[field_name]
                                break

                    if payload_value:

                        # If it's a dict with transition_id, only apply if transition doesn't exist
                        # (fallback case) or if it's a simple value (not transition-specific)
                        if (
                            isinstance(payload_value, dict)
                            and "value" in payload_value
                            and "transition_id" in payload_value
                        ):
                            # Check if the specified transition exists
                            target_transition_id = payload_value.get("transition_id")
                            logger.debug(
                                f"Target transition for field '{field_name}': '{target_transition_id}'"
                            )
                            if target_transition_id not in transitions_by_id:
                                # If transition doesn't exist, use the value as fallback
                                logger.debug(
                                    f"Target transition '{target_transition_id}' doesn't exist, using as fallback for '{transition_id}'"
                                )
                                item["field_value"] = payload_value["value"]
                                field_updated = True
                            else:
                                logger.debug(
                                    f"Target transition '{target_transition_id}' exists, NOT applying to '{transition_id}'"
                                )
                            # If transition exists but doesn't match current transition, don't apply
                        else:
                            # For simple values (not transition-specific), apply to all matching fields
                            logger.debug(
                                f"Simple value for field '{field_name}', applying to transition '{transition_id}'"
                            )
                            parsed_value = _parse_json_if_needed(
                                payload_value, field_name, transition, workflow
                            )
                            item["field_value"] = parsed_value
                            field_updated = True

                    # If still not updated and it's a required field, raise error
                    # Only raise error if we've checked all transitions and none have been updated
                    # We'll track this separately and check at the end of processing all transitions
                    if not field_updated:
                        # Check if field exists in either format
                        field_exists = False
                        if isinstance(user_payload_template, dict):
                            field_exists = field_name in user_payload_template
                        elif isinstance(user_payload_template, list):
                            # Check if field exists in any array entry
                            for entry in user_payload_template:
                                if isinstance(entry, dict) and field_name in entry:
                                    field_exists = True
                                    break

                        if not field_exists:
                            raise ValueError(
                                f"Missing value for required user-dependent field: '{field_name}'"
                            )

                # Handle fields with None values that weren't updated yet
                elif not field_updated and field_value is None:
                    payload_value = None
                    if isinstance(user_payload_template, dict) and field_name in user_payload_template:
                        payload_value = user_payload_template[field_name]
                    elif isinstance(user_payload_template, list):
                        # Array format - find any matching field (fallback)
                        for entry in user_payload_template:
                            if isinstance(entry, dict) and field_name in entry:
                                payload_value = entry[field_name]
                                break

                    if payload_value:

                        # If it's a dict with transition_id, only apply if transition doesn't exist
                        # (fallback case) or if it's a simple value (not transition-specific)
                        if (
                            isinstance(payload_value, dict)
                            and "value" in payload_value
                            and "transition_id" in payload_value
                        ):
                            # Check if the specified transition exists
                            target_transition_id = payload_value.get("transition_id")
                            if target_transition_id not in transitions_by_id:
                                # If transition doesn't exist, use the value as fallback
                                extracted_value = payload_value["value"]
                                parsed_value = _parse_json_if_needed(
                                    extracted_value, field_name, transition, workflow
                                )
                                item["field_value"] = parsed_value
                            # If transition exists but doesn't match current transition, don't apply
                        else:
                            # For simple values (not transition-specific), apply to all matching fields
                            parsed_value = _parse_json_if_needed(
                                payload_value, field_name, transition, workflow
                            )
                            item["field_value"] = parsed_value
                    else:
                        # Get the node_id to find the corresponding input schema
                        node_id = transition.get("node_info", {}).get("node_id")
                        # Check if the field is required in the input schema
                        is_required = False

                        # Find the node in the nodes array
                        for node in workflow.get("nodes", []):
                            if node.get("id") == node_id:
                                # Check each server tool's input schema
                                for server_tool in node.get("server_tools", []):
                                    # Look for the field in predefined_fields
                                    for field in server_tool.get(
                                        "input_schema", {}
                                    ).get("predefined_fields", []):
                                        if field.get(
                                            "field_name"
                                        ) == field_name and field.get(
                                            "required", False
                                        ):
                                            is_required = True
                                            break
                                    if is_required:
                                        break
                            if is_required:
                                break

                        # Only throw error if the field is required AND doesn't have a handle mapping
                        if is_required:
                            # Check if this field has a handle mapping that will resolve it later
                            has_handle_mapping = _has_handle_mapping_for_field(
                                field_name, transition, workflow
                            )

                            if not has_handle_mapping:
                                raise ValueError(
                                    f"Missing value for required field: '{field_name}'"
                                )

        # After processing all tool parameters, update loop_config if this is a loop transition
        _update_loop_config_from_tool_params(transition)

        # Special handling for conditional components: inject user input into global_context
        logger.debug(
            f"Calling _inject_user_input_for_conditional_components for {transition_id}"
        )
        _inject_user_input_for_conditional_components(
            transition, transition_id, user_payload_template
        )

        conditional_routing = transition.get("conditional_routing", {})
        global_context_defs = conditional_routing.get("global_context_definitions", {})

        if "variables" in global_context_defs:
            for variable in global_context_defs["variables"]:
                var_name = variable.get("name")
                current_value = variable.get("value")

                if var_name in global_context_values:
                    # Check if the global context value is a dictionary with transition_id
                    gc_value = global_context_values[var_name]
                    if (
                        isinstance(gc_value, dict)
                        and "value" in gc_value
                        and "transition_id" in gc_value
                    ):
                        # If this is the specified transition, update the variable
                        if gc_value["transition_id"] == transition_id:
                            variable["value"] = gc_value["value"]
                        # If the specified transition doesn't exist, use as fallback
                        elif gc_value["transition_id"] not in transitions_by_id:
                            variable["value"] = gc_value["value"]
                    else:
                        # For simple values, update as before
                        variable["value"] = gc_value

                elif current_value is None:
                    raise ValueError(
                        f"Missing value for required global context variable: '{var_name}'"
                    )

    # Preserve the payload structure in the workflow for EnhancedWorkflowEngine
    # The EnhancedWorkflowEngine expects to find user_payload_template in the workflow
    workflow["payload"] = params["payload"]
    logger.debug(f"Preserved payload structure in workflow: {workflow['payload']}")

    return workflow
