"""
Semantic Type Extraction Utility

This module provides utilities for detecting semantic type information from actual data content.
Instead of relying on schema format fields, it analyzes the data itself to determine the
appropriate semantic type (e.g., video, audio, image, url, email, etc.).

The semantic types help frontend applications render elements appropriately based on the
data's actual content and meaning.
"""

from typing import Dict, Any, Optional
import logging
import mimetypes
import re

# Try to import enhanced logger, fall back to standard logging for testing
try:
    from app.utils.enhanced_logger import get_logger

    logger = get_logger("SemanticTypeExtractor")
except Exception:
    logger = logging.getLogger("SemanticTypeExtractor")


def _detect_semantic_type_from_url_pattern(url: str) -> Optional[str]:
    """
    Detect semantic type from URL patterns when MIME type detection fails.

    This function uses regex patterns to detect file extensions in URLs
    that might not be properly detected by mimetypes.guess_type().

    Args:
        url: URL string to analyze

    Returns:
        str or None: Detected semantic type or None if not recognized
    """
    url_lower = url.lower()

    # Define patterns for file extensions that might be missed by MIME detection
    # Pattern matches extension followed by query params, fragments, trailing slash, or end of string
    extension_patterns = {
        r"\.pptx(?:\?|#|/|$)": "ppt",
        r"\.ppt(?:\?|#|/|$)": "ppt",
        r"\.mp4(?:\?|#|/|$)": "video",
        r"\.avi(?:\?|#|/|$)": "video",
        r"\.mov(?:\?|#|/|$)": "video",
        r"\.webm(?:\?|#|/|$)": "video",
        r"\.mp3(?:\?|#|/|$)": "audio",
        r"\.wav(?:\?|#|/|$)": "audio",
        r"\.flac(?:\?|#|/|$)": "audio",
        r"\.jpg(?:\?|#|/|$)": "image",
        r"\.jpeg(?:\?|#|/|$)": "image",
        r"\.png(?:\?|#|/|$)": "image",
        r"\.gif(?:\?|#|/|$)": "image",
        r"\.webp(?:\?|#|/|$)": "image",
        r"\.pdf(?:\?|#|/|$)": "file",
        r"\.docx(?:\?|#|/|$)": "file",
        r"\.xlsx(?:\?|#|/|$)": "file",
    }

    for pattern, semantic_type in extension_patterns.items():
        if re.search(pattern, url_lower):
            logger.debug(
                f"Pattern '{pattern}' matched in URL, returning semantic type '{semantic_type}'"
            )
            return semantic_type

    return None


def detect_semantic_type_from_data(data: Any) -> str:
    """
    Detect semantic type from actual data content.

    This function analyzes the actual data to determine its semantic type,
    providing more accurate type detection than schema-based approaches.

    Args:
        data: The actual data value to analyze

    Returns:
        str: Detected semantic type based on data content

    Examples:
        >>> detect_semantic_type_from_data("https://example.com/video.mp4")
        'video'
        >>> detect_semantic_type_from_data("<EMAIL>")
        'email'
        >>> detect_semantic_type_from_data(42)
        'number'
    """
    # Handle non-string primitive types first
    # Note: Check bool before int/float since bool is a subclass of int in Python
    if isinstance(data, bool):
        return "boolean"
    elif isinstance(data, (int, float)):
        return "number"
    elif isinstance(data, list):
        return "array"
    elif isinstance(data, dict):
        return "object"
    elif data is None:
        return "null"
    elif not isinstance(data, str):
        return "string"  # fallback for other types

    # String data analysis
    data_str = data.strip()

    # Check for email first (before URL/file checks)
    if re.match(r"^[\w\.-]+@[\w\.-]+\.\w+$", data_str):
        return "email"

    # Check for URLs and get MIME type from extension
    if data_str.startswith("http://") or data_str.startswith("https://"):
        mime, _ = mimetypes.guess_type(data_str)
        semantic_type = mime_to_semantic(mime)
        logger.debug(
            f"URL semantic type detection - URL: {data_str[:100]}, MIME: {mime}, semantic_type: {semantic_type}, truthy: {bool(semantic_type)}"
        )
        if semantic_type:
            logger.debug(
                f"Detected semantic type '{semantic_type}' from URL: {data_str}"
            )
            return semantic_type

        # If MIME type detection failed, try pattern-based detection for known extensions
        semantic_type_from_pattern = _detect_semantic_type_from_url_pattern(data_str)
        if semantic_type_from_pattern:
            logger.debug(
                f"Detected semantic type '{semantic_type_from_pattern}' from URL pattern: {data_str[:100]}"
            )
            return semantic_type_from_pattern

        logger.debug(
            f"No semantic type detected from MIME or pattern, falling back to 'url' for: {data_str[:100]}"
        )
        return "url"

    # File extension check (e.g. "example.mp3")
    if re.match(r"^.+\.[a-zA-Z0-9]+$", data_str):
        mime, _ = mimetypes.guess_type(data_str)
        semantic_type = mime_to_semantic(mime)
        if semantic_type:
            logger.debug(
                f"Detected semantic type '{semantic_type}' from file extension: {data_str}"
            )
            return semantic_type
        return "file"

    # Check for specific data patterns
    if re.match(r"^\{.*\}$", data_str) or re.match(r"^\[.*\]$", data_str):
        return "json"
    if re.match(r"^\d{4}-\d{2}-\d{2}", data_str):
        return "datetime"
    if data_str.lower().startswith("<!doctype html") or "<html" in data_str.lower():
        return "html"
    if data_str.lower().startswith("data:"):
        return "data_url"

    return "string"


def mime_to_semantic(mime_type: str) -> Optional[str]:
    """
    Convert MIME type to semantic type.

    Args:
        mime_type: MIME type string (e.g., "video/mp4")

    Returns:
        str or None: Corresponding semantic type or None if not recognized
    """
    if mime_type is None:
        return None
    if mime_type.startswith("video/"):
        return "video"
    elif mime_type.startswith("audio/"):
        return "audio"
    elif mime_type.startswith("image/"):
        return "image"
    elif mime_type in ["application/json"]:
        return "json"
    elif mime_type in ["text/html"]:
        return "html"
    elif mime_type in [
        "application/vnd.openxmlformats-officedocument.presentationml.presentation",
        "application/vnd.ms-powerpoint",
    ]:
        return "ppt"
    elif mime_type.startswith("text/"):
        return "text"
    return mime_type.split("/")[0]


def extract_semantic_type(
    field_definition: Dict[str, Any], data_value: Any = None
) -> str:
    """
    Extract semantic type from data content with field definition fallback.

    This function prioritizes data-driven detection over schema format fields,
    providing more accurate semantic type detection.

    Args:
        field_definition: Field definition dictionary (for fallback)
        data_value: The actual data value to analyze (preferred)

    Returns:
        str: Detected semantic type
    """
    # First, try to detect from actual data if provided
    if data_value is not None:
        detected_type = detect_semantic_type_from_data(data_value)
        logger.debug(f"Detected semantic type '{detected_type}' from data content")
        return detected_type

    # Fallback to schema format if no data provided
    if not isinstance(field_definition, dict):
        logger.warning(
            f"Invalid field_definition type: {type(field_definition)}. Expected dict."
        )
        return "string"

    data_type = field_definition.get("data_type", {})
    if not isinstance(data_type, dict):
        logger.warning(
            f"Invalid data_type in field_definition: {type(data_type)}. Expected dict."
        )
        return "string"

    # Check for explicit format in schema
    format_value = data_type.get("format")
    if format_value and isinstance(format_value, str):
        semantic_type = format_value.strip().lower()
        if semantic_type:
            logger.debug(f"Using schema format semantic type '{semantic_type}'")
            return semantic_type

    # Fallback to data type
    data_type_value = data_type.get("type", "string")
    logger.debug(f"Using data type '{data_type_value}' as semantic type")
    return data_type_value


def extract_semantic_type_for_nested(
    field_definition: Dict[str, Any], data_type: str, data_value: Any = None
) -> str:
    """
    Extract semantic type for nested data structures (arrays, objects).

    Uses data-driven detection when possible, with schema fallback.

    Args:
        field_definition: Field definition dictionary
        data_type: The primary data type ("array", "object", etc.)
        data_value: The actual data value to analyze (optional)

    Returns:
        str: Semantic type for the nested structure
    """
    # First, try to detect from actual data if provided
    if data_value is not None:
        detected_type = detect_semantic_type_from_data(data_value)
        logger.debug(
            f"Detected nested semantic type '{detected_type}' from data content"
        )
        return detected_type

    if not isinstance(field_definition, dict):
        logger.warning(
            f"Invalid field_definition type for nested extraction: {type(field_definition)}"
        )
        return (
            data_type
            if data_type in ["array", "object", "number", "boolean"]
            else "string"
        )

    # For arrays, default to "array" unless schema specifies otherwise
    if data_type == "array":
        data_type_info = field_definition.get("data_type", {})

        # Check if the array itself has format information
        array_format = data_type_info.get("format")
        if array_format and isinstance(array_format, str):
            semantic_type = array_format.strip().lower()
            if semantic_type:
                logger.debug(
                    f"Using schema format semantic type '{semantic_type}' for array"
                )
                return semantic_type

        # Default to "array" - don't create "array_of_" types unless explicitly specified
        return "array"

    # For objects, default to "object" unless schema specifies otherwise
    elif data_type == "object":
        data_type_info = field_definition.get("data_type", {})
        object_format = data_type_info.get("format")

        if object_format and isinstance(object_format, str):
            semantic_type = object_format.strip().lower()
            if semantic_type:
                logger.debug(
                    f"Using schema format semantic type '{semantic_type}' for object"
                )
                return semantic_type

        return "object"

    # For other types, fall back to standard extraction
    return extract_semantic_type(field_definition, data_value)


def get_supported_semantic_types() -> Dict[str, list]:
    """
    Get a dictionary of all supported semantic types organized by category.

    Returns:
        Dict[str, list]: Dictionary with categories as keys and lists of semantic types as values
    """
    return {
        "communication": ["email", "url", "link", "uri", "href"],
        "datetime": [
            "datetime",
            "date",
            "time",
            "timestamp",
            "created",
            "updated",
            "modified",
        ],
        "media": [
            "audio",
            "video",
            "image",
            "audio_url",
            "video_url",
            "image_url",
            "audio_file",
            "video_file",
            "image_file",
        ],
        "system": ["file_path", "identifier", "id", "uuid", "guid", "status", "color"],
        "numeric": ["currency", "money", "percentage", "percent"],
        "default": ["string", "text"],
    }


def validate_semantic_type(semantic_type: str) -> bool:
    """
    Validate if a semantic type is supported.

    Args:
        semantic_type: The semantic type to validate

    Returns:
        bool: True if the semantic type is supported, False otherwise
    """
    if not isinstance(semantic_type, str):
        return False

    supported_types = get_supported_semantic_types()
    semantic_type_lower = semantic_type.lower().strip()

    for category_types in supported_types.values():
        if semantic_type_lower in category_types:
            return True

    return False


def normalize_semantic_type(semantic_type: str) -> str:
    """
    Normalize a semantic type to a standard format.

    This function handles common variations and aliases for semantic types.

    Args:
        semantic_type: The semantic type to normalize

    Returns:
        str: Normalized semantic type
    """
    if not isinstance(semantic_type, str):
        return "string"

    normalized = semantic_type.lower().strip()

    # Handle common aliases
    aliases = {
        "uri": "url",
        "href": "url",
        "link": "url",
        "mail": "email",
        "e-mail": "email",
        "timestamp": "datetime",
        "created": "datetime",
        "updated": "datetime",
        "modified": "datetime",
        "money": "currency",
        "percent": "percentage",
        "id": "identifier",
        "uuid": "identifier",
        "guid": "identifier",
        "text": "string",
    }

    return aliases.get(normalized, normalized)
