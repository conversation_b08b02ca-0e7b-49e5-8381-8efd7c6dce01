# credential_service.py
import json
import logging
import time
from typing import Optional, Dict, Any
import aiohttp
from app.config.config import settings
from app.schemas.credential_service import CredentialRetrievalError


class CredentialService:
    """
    Service for retrieving OAuth credentials from the authentication API.
    Implements caching to reduce API calls and improve performance.
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.api_base_url = settings.api_base_url.rstrip("/")
        self.server_auth_key = settings.server_auth_key
        self.cache_ttl = settings.credential_cache_ttl

        # Simple in-memory cache with TTL
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, float] = {}

        self.logger.info(
            f"CredentialService initialized with API base URL: {self.api_base_url}"
        )

    def _is_cache_valid(self, composite_key: str) -> bool:
        """Check if cached credential is still valid based on TTL."""
        if composite_key not in self._cache_timestamps:
            return False

        elapsed = time.time() - self._cache_timestamps[composite_key]
        return elapsed < self.cache_ttl

    def _get_from_cache(self, composite_key: str) -> Optional[Dict[str, Any]]:
        """Retrieve credential from cache if valid."""
        if self._is_cache_valid(composite_key):
            self.logger.debug(f"Cache hit for composite key: {composite_key}")
            return self._cache.get(composite_key)
        else:
            # Clean up expired cache entry
            if composite_key in self._cache:
                del self._cache[composite_key]
                del self._cache_timestamps[composite_key]
                self.logger.debug(f"Expired cache entry removed for: {composite_key}")
            return None

    def _store_in_cache(self, composite_key: str, credentials: Dict[str, Any]) -> None:
        """Store credentials in cache with current timestamp."""
        self._cache[composite_key] = credentials
        self._cache_timestamps[composite_key] = time.time()
        self.logger.debug(f"Credentials cached for: {composite_key}")

    async def get_oauth_credentials(
        self, user_id: str, tool_name: str, provider: str = "google"
    ) -> Dict[str, Any]:
        """
        Retrieve OAuth credentials for the given user_id, tool_name, and provider.

        Args:
            user_id: The user identifier
            tool_name: The name of the tool requiring authentication
            provider: The OAuth provider (e.g., "google", "microsoft", "slack")

        Returns:
            Dictionary containing OAuth credentials

        Raises:
            CredentialRetrievalError: If credentials cannot be retrieved
        """
        # Create composite key using new format (without mcp_id)
        composite_key = f"{user_id}_{tool_name}_{provider}"

        self.logger.info(
            f"Retrieving OAuth credentials for composite key: {composite_key}"
        )

        # Check cache first
        cached_credentials = self._get_from_cache(composite_key)
        if cached_credentials:
            return cached_credentials

        # Validate server auth key
        if not self.server_auth_key:
            raise CredentialRetrievalError(
                "Server authentication key not configured. Cannot retrieve credentials."
            )

        # Prepare API request - using the correct API Gateway endpoint
        url = f"{self.api_base_url}/api/v1/oauth/server/credentials"
        headers = {
            "X-Server-Auth-Key": self.server_auth_key,
            "Content-Type": "application/json",
        }
        params = {
            "user_id": user_id,
            "tool_name": tool_name,
            "provider": provider,
        }

        try:
            async with aiohttp.ClientSession() as session:
                self.logger.debug(f"Making API request to: {url}")
                async with session.get(url, headers=headers, params=params) as response:
                    response_text = await response.text()

                    if response.status == 200:
                        credentials = await response.json()

                        if credentials.get("success"):
                            # Store in cache
                            self._store_in_cache(composite_key, credentials)
                            self.logger.info(
                                f"Successfully retrieved credentials for: {composite_key}"
                            )
                            return credentials
                        else:
                            error_msg = credentials.get(
                                "message", "Unknown error from API"
                            )
                            raise CredentialRetrievalError(
                                f"API returned error: {error_msg}"
                            )

                    elif response.status == 404:
                        raise CredentialRetrievalError(
                            f"No OAuth credentials found for user: {user_id}, tool: {tool_name}"
                        )

                    elif response.status == 401:
                        raise CredentialRetrievalError(
                            "Server authentication failed. Invalid server auth key."
                        )

                    elif response.status == 403:
                        raise CredentialRetrievalError(
                            "Server authentication forbidden. Insufficient permissions."
                        )

                    else:
                        self.logger.error(
                            f"API request failed with status {response.status}: {response_text}"
                        )
                        raise CredentialRetrievalError(
                            f"Failed to retrieve credentials. API returned status {response.status}"
                        )

        except aiohttp.ClientError as e:
            self.logger.error(f"Network error retrieving credentials: {e}")
            raise CredentialRetrievalError(f"Network error: {e}")

        except json.JSONDecodeError as e:
            self.logger.error(f"Failed to parse API response as JSON: {e}")
            raise CredentialRetrievalError(f"Invalid JSON response from API: {e}")

        except Exception as e:
            self.logger.error(
                f"Unexpected error retrieving credentials: {e}", exc_info=True
            )
            raise CredentialRetrievalError(f"Unexpected error: {e}")

    def clear_cache(self, composite_key: Optional[str] = None) -> None:
        """
        Clear cached credentials.

        Args:
            composite_key: If provided, clear only this specific key.
                          If None, clear all cached credentials.
        """
        if composite_key:
            if composite_key in self._cache:
                del self._cache[composite_key]
                del self._cache_timestamps[composite_key]
                self.logger.info(f"Cleared cache for: {composite_key}")
        else:
            self._cache.clear()
            self._cache_timestamps.clear()
            self.logger.info("Cleared all cached credentials")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        valid_entries = sum(
            1 for key in self._cache.keys() if self._is_cache_valid(key)
        )
        return {
            "total_entries": len(self._cache),
            "valid_entries": valid_entries,
            "expired_entries": len(self._cache) - valid_entries,
            "cache_ttl": self.cache_ttl,
        }
