# kafka_service.py
import asyncio
import json
import logging
import signal
from typing import Optional, Any

from aiokafka import AIOKafkaConsumer, AIOKafkaProducer  # type: ignore
from aiokafka.structs import TopicPartition  # type: ignore
from aiokafka.errors import KafkaError, IllegalStateError  # type: ignore
from app.config.config import settings
from app.core_.mcp_executor import MCPExecutor
from app.core_.error_handler import format_kafka_error_response, create_validation_error
from app.core_.exceptions import MCPExecutorError, ErrorCategory, ErrorCode
from app.services.credential_service import CredentialService, CredentialRetrievalError
from werkzeug.exceptions import InternalServerError  # type: ignore


print(settings.kafka_bootstrap_servers)
print(settings.kafka_consumer_topic)
print(settings.kafka_results_topic)
print(settings.kafka_consumer_group_id)


class InfiniteSemaphore(asyncio.Semaphore):
    """A semaphore that never times out when acquiring."""

    async def acquire(self):
        """Acquire the semaphore with no timeout."""
        # Call the parent's acquire method directly without timeout
        # This will wait indefinitely until a slot is available
        return await super().acquire()


class KafkaMCPService:

    def __init__(self):
        self.logger = logging.getLogger(__name__)

        # Initialize configuration values
        self._load_config()

        # Initialize Kafka components
        self._init_kafka_components()

        self.mcp_executor = MCPExecutor(producer=self.producer, logger=self.logger)
        self._consumer_task: Optional[asyncio.Task] = None

        # Setup signal handler for config reload (SIGUSR1)
        self._setup_config_reload_signal()

    def _load_config(self):
        """Load configuration values from settings."""
        self.kafka_broker: str = settings.kafka_bootstrap_servers
        self.consumer_topic: str = settings.kafka_consumer_topic
        self.group_id: str = settings.kafka_consumer_group_id
        self.reply_topic = settings.kafka_results_topic
        self.max_concurrent_tasks = settings.max_concurrent_tasks

        self.logger.info(
            f"Configuration loaded - Broker: {self.kafka_broker}, "
            f"Consumer Topic: {self.consumer_topic}, Group ID: {self.group_id}"
        )

    def _init_kafka_components(self):
        """Initialize Kafka consumer and producer with current config."""
        try:
            self.consumer: AIOKafkaConsumer = AIOKafkaConsumer(
                self.consumer_topic,
                bootstrap_servers=self.kafka_broker,
                group_id=self.group_id,
                auto_offset_reset="latest",
                enable_auto_commit=True,
            )
            self.producer: AIOKafkaProducer = AIOKafkaProducer(
                bootstrap_servers=self.kafka_broker,
                max_request_size=524288000,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
            )
        except RuntimeError as e:
            if "async function" in str(e):
                # Kafka components require async context, defer initialization
                self.logger.warning(
                    "Kafka components require async context, "
                    "will initialize when consumer starts"
                )
                self.consumer = None
                self.producer = None
            else:
                raise

        # Initialize additional services
        self.credential_service = CredentialService()

        # Use InfiniteSemaphore instead of standard asyncio.Semaphore to prevent timeout issues
        self.semaphore = InfiniteSemaphore(self.max_concurrent_tasks)

    def _setup_config_reload_signal(self):
        """Setup signal handler for runtime config reloading."""
        try:
            # Use SIGUSR1 for config reload (Unix systems only)
            signal.signal(signal.SIGUSR1, self._handle_config_reload_signal)
            self.logger.info(
                "Config reload signal handler setup complete. "
                "Send SIGUSR1 to reload configuration."
            )
        except (AttributeError, OSError) as e:
            self.logger.warning(
                f"Cannot setup config reload signal handler: {e}. "
                "Runtime config reload via signal not available."
            )

    def _handle_config_reload_signal(
        self, signum, frame
    ):  # pylint: disable=unused-argument
        """Handle config reload signal."""
        self.logger.info("🔄 Received SIGUSR1 signal - reloading configuration...")
        try:
            # Schedule async config reload
            loop = asyncio.get_event_loop()
            if loop.is_running():
                asyncio.create_task(self.reload_config())
            else:
                self.logger.warning("Event loop not running, cannot reload config")
        except Exception as e:
            self.logger.error(f"Error handling config reload signal: {e}")

    async def reload_config(self):
        """Reload configuration from environment variables at runtime."""
        try:
            self.logger.info("🔄 Reloading configuration from environment variables...")

            # Store old values for comparison
            old_broker = self.kafka_broker
            old_consumer_topic = self.consumer_topic
            old_max_tasks = self.max_concurrent_tasks

            # Load new configuration
            self._load_config()

            # Check if critical Kafka settings changed
            kafka_changed = (
                old_broker != self.kafka_broker
                or old_consumer_topic != self.consumer_topic
            )

            if kafka_changed:
                self.logger.warning(
                    "⚠️  Critical Kafka settings changed. "
                    "Server restart recommended for full effect."
                )

            # Update semaphore if max concurrent tasks changed
            if old_max_tasks != self.max_concurrent_tasks:
                self.semaphore = InfiniteSemaphore(self.max_concurrent_tasks)
                self.logger.info(
                    f"Updated max concurrent tasks: {old_max_tasks} → "
                    f"{self.max_concurrent_tasks}"
                )

            self.logger.info("✅ Configuration reloaded successfully")

        except Exception as e:
            self.logger.error(f"❌ Failed to reload configuration: {e}", exc_info=True)

    async def start_consumer(self) -> None:
        """Starts the Kafka consumer and producer, and runs the message processing loop."""
        self.logger.info("Starting Kafka consumer and producer...")

        # Initialize Kafka components if not already done (deferred initialization)
        if self.consumer is None or self.producer is None:
            self.logger.info("Initializing Kafka components in async context...")
            self._init_kafka_components()

        # Send service startup notification
        await self._send_service_status_update(
            "starting", "Service initialization in progress"
        )

        connection_failures = 0
        max_connection_failures = 5
        base_retry_delay = 2

        try:
            while True:
                try:
                    await self.consumer.start()
                    await self.producer.start()
                    self.logger.info(
                        "Kafka consumer and producer started successfully."
                    )

                    # Reset connection failure counter on successful connection
                    connection_failures = 0

                    # Send service ready notification
                    await self._send_service_status_update(
                        "running", "Service is ready and processing messages"
                    )

                except KafkaError as e:
                    connection_failures += 1
                    self.logger.error(
                        f"Failed to start Kafka consumer/producer (attempt {connection_failures}/{max_connection_failures}): {e}",
                        exc_info=True,
                    )

                    # Send connection failure notification
                    await self._send_service_status_update(
                        "error",
                        f"Kafka connection failed: {str(e)}",
                        error_details={
                            "error_type": "kafka_connection_failed",
                            "attempt": connection_failures,
                            "max_attempts": max_connection_failures,
                            "exception_type": type(e).__name__,
                        },
                    )

                    # Implement circuit breaker pattern
                    if connection_failures >= max_connection_failures:
                        error_msg = f"Failed to connect to Kafka after {max_connection_failures} attempts"
                        self.logger.critical(error_msg)
                        await self._send_service_status_update("failed", error_msg)
                        raise MCPExecutorError(
                            error_msg,
                            error_category=ErrorCategory.KAFKA_ERROR,
                            error_code=ErrorCode.KAFKA_CONNECTION_FAILED,
                            details={
                                "attempts": connection_failures,
                                "last_error": str(e),
                            },
                            retryable=False,
                        ) from e

                    # Exponential backoff
                    retry_delay = base_retry_delay * (2 ** (connection_failures - 1))
                    self.logger.info(
                        f"Retrying Kafka connection in {retry_delay} seconds..."
                    )
                    await asyncio.sleep(retry_delay)
                    continue

                try:
                    message_count = 0
                    async for msg in self.consumer:
                        message_count += 1
                        self.logger.info(
                            f"Received message #{message_count}: Topic={msg.topic}, Partition={msg.partition}, Offset={msg.offset}"
                        )
                        await self.semaphore.acquire()
                        asyncio.create_task(self.process_message(msg, self.semaphore))

                except IllegalStateError as illegal_state:
                    self.logger.warning(
                        "Kafka consumer is not running. Likely stopped."
                    )
                    # Send consumer state error notification
                    await self._send_service_status_update(
                        "warning",
                        "Kafka consumer stopped unexpectedly",
                        error_details={
                            "error_type": "consumer_illegal_state",
                            "exception_type": type(illegal_state).__name__,
                        },
                    )
                except asyncio.CancelledError:
                    self.logger.info("Consumer task cancelled.")
                    await self._send_service_status_update(
                        "stopping", "Consumer task cancelled"
                    )
                    raise
                except KafkaError as kafka_error:
                    self.logger.error(
                        f"Kafka error in consumer loop: {kafka_error}", exc_info=True
                    )
                    # Send Kafka error notification
                    await self._send_service_status_update(
                        "error",
                        f"Kafka consumer error: {str(kafka_error)}",
                        error_details={
                            "error_type": "kafka_consumer_error",
                            "exception_type": type(kafka_error).__name__,
                            "messages_processed": message_count,
                        },
                    )
                    # Break inner loop to retry connection
                    break
                except Exception as e:
                    self.logger.error(
                        f"Unexpected error in consumer loop: {e}", exc_info=True
                    )
                    # Send unexpected error notification
                    await self._send_service_status_update(
                        "error",
                        f"Unexpected consumer error: {str(e)}",
                        error_details={
                            "error_type": "unexpected_consumer_error",
                            "exception_type": type(e).__name__,
                            "messages_processed": message_count,
                        },
                    )
                    # Break inner loop to retry connection
                    break

        except asyncio.CancelledError:
            self.logger.info("Consumer task cancelled.")
            await self._send_service_status_update(
                "stopping", "Consumer task cancelled"
            )
        except Exception as e:
            self.logger.error(f"Fatal error in consumer startup: {e}", exc_info=True)
            await self._send_service_status_update(
                "failed",
                f"Fatal consumer error: {str(e)}",
                error_details={
                    "error_type": "fatal_consumer_error",
                    "exception_type": type(e).__name__,
                },
            )
            raise
        finally:
            self.logger.info("Consumer loop finished or terminated. Cleaning up...")
            await self._send_service_status_update(
                "stopping", "Consumer cleanup in progress"
            )
            await self.stop_consumer()
            await self._send_service_status_update(
                "stopped", "Consumer stopped and cleaned up"
            )

    async def _send_service_status_update(
        self, status: str, message: str, error_details: Optional[dict] = None
    ) -> None:
        """Send service status updates to a dedicated topic for monitoring."""
        try:
            from datetime import datetime, timezone
            import socket

            status_message = {
                "service_name": "mcp-executor-service",
                "instance_id": socket.gethostname(),
                "status": status,
                "message": message,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            if error_details:
                status_message["error_details"] = error_details

            # Use a dedicated topic for service status updates
            status_topic = "service-status-updates"

            if (
                self.producer
                and hasattr(self.producer, "_sender")
                and self.producer._sender._running
            ):
                await self.producer.send_and_wait(status_topic, value=status_message)
                self.logger.debug(f"Sent service status update: {status_message}")
            else:
                # If producer is not available, just log the status
                self.logger.info(
                    f"Service status update (producer unavailable): {status_message}"
                )

        except Exception as e:
            # Don't let status update failures affect the main service
            self.logger.warning(f"Failed to send service status update: {e}")

    async def stop_consumer(self) -> None:
        """Stops the Kafka consumer and producer with enhanced error handling."""
        self.logger.info("Attempting to stop Kafka consumer and producer...")

        # Stop consumer
        if (
            self.consumer
            and hasattr(self.consumer, "_running")
            and self.consumer._running
        ):
            try:
                await self.consumer.stop()
                self.logger.info("Kafka consumer stopped successfully.")
            except KafkaError as kafka_error:
                self.logger.error(
                    f"Kafka error stopping consumer: {kafka_error}", exc_info=True
                )
                # Send error notification
                await self._send_service_status_update(
                    "error",
                    f"Error stopping Kafka consumer: {str(kafka_error)}",
                    error_details={
                        "error_type": "kafka_consumer_stop_error",
                        "exception_type": type(kafka_error).__name__,
                    },
                )
            except Exception as e:
                self.logger.error(
                    f"Unexpected error stopping Kafka consumer: {e}", exc_info=True
                )
                # Send error notification
                await self._send_service_status_update(
                    "error",
                    f"Unexpected error stopping consumer: {str(e)}",
                    error_details={
                        "error_type": "unexpected_consumer_stop_error",
                        "exception_type": type(e).__name__,
                    },
                )
        else:
            self.logger.info("Kafka consumer already stopped or not started.")

        # Stop producer
        if (
            self.producer
            and hasattr(self.producer, "_sender")
            and hasattr(self.producer._sender, "_running")
            and self.producer._sender._running
        ):
            try:
                await self.producer.stop()
                self.logger.info("Kafka producer stopped successfully.")
            except KafkaError as kafka_error:
                self.logger.error(
                    f"Kafka error stopping producer: {kafka_error}", exc_info=True
                )
                # Note: Can't send status update since producer is being stopped
                # Just log the error
            except Exception as e:
                self.logger.error(
                    f"Unexpected error stopping Kafka producer: {e}", exc_info=True
                )
        else:
            self.logger.info("Kafka producer already stopped or not started.")

    async def send_error_response(
        self,
        reply_topic: str,
        error_message: str,
        request_id: str,
        error_type: str = "system_error",
        error_code: str = "internal_error",
        retryable: bool = False,
        correlation_id: Optional[str] = None,
        details: Optional[dict] = None,
    ) -> None:
        """Send enhanced error response to Kafka with categorization."""
        from datetime import datetime, timezone

        message = {
            "request_id": request_id,
            "error": error_message,
            "error_type": error_type,
            "error_code": error_code,
            "retryable": retryable,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "mcp_status": "error",
        }

        if correlation_id:
            message["correlation_id"] = correlation_id

        if details:
            message["details"] = details

        if reply_topic:
            try:
                await self.producer.send_and_wait(reply_topic, value=message)
                self.logger.info(
                    f"Sent error response to topic '{reply_topic}': {message}"
                )
            except Exception as e:
                self.logger.error(
                    f"Failed to send error response to Kafka: {e}", exc_info=True
                )

    async def send_error_response_from_exception(
        self,
        reply_topic: str,
        exception: Exception,
        request_id: str,
        correlation_id: Optional[str] = None,
        additional_context: Optional[dict] = None,
    ) -> None:
        """Send error response from exception using error handler."""
        try:
            kafka_error = format_kafka_error_response(
                exception, request_id, correlation_id, additional_context
            )

            if reply_topic:
                await self.producer.send_and_wait(reply_topic, value=kafka_error)
                self.logger.info(
                    f"Sent error response to topic '{reply_topic}': {kafka_error}"
                )
        except Exception as e:
            self.logger.error(
                f"Failed to send error response to Kafka: {e}", exc_info=True
            )

    async def process_message(self, msg, semaphore: asyncio.Semaphore):
        """Processes a single Kafka message, executes the MCP tool, and handles commits."""
        # Release the semaphore immediately after task creation
        # This allows the consumer to process more messages without waiting for this one to complete
        semaphore.release()
        self.logger.info(
            f"Semaphore released early. Current queue size: {self.semaphore._value}"
        )

        self.logger.info(f"Processing message: {msg}")

        topic_partition = TopicPartition(msg.topic, msg.partition)
        offset = msg.offset
        commit_offset = True

        payload = None
        try:
            payload = json.loads(msg.value.decode("utf-8"))
            self.logger.info(f"Processing request from offset {offset}: {payload}")

            # ===== PHASE 1: STRICT PAYLOAD VALIDATION =====
            # Extract and validate REQUIRED parameters first
            validation_errors = []

            # Required field: mcp_id
            mcp_id = payload.get("mcp_id")
            if not mcp_id or not isinstance(mcp_id, str) or not mcp_id.strip():
                validation_errors.append(
                    "mcp_id is required and must be a non-empty string"
                )

            # Required field: user_id
            user_id = payload.get("user_id")
            if not user_id or not isinstance(user_id, str) or not user_id.strip():
                validation_errors.append(
                    "user_id is required and must be a non-empty string"
                )

            # Required field: tool_name
            tool_name = payload.get("tool_name")
            if not tool_name or not isinstance(tool_name, str) or not tool_name.strip():
                validation_errors.append(
                    "tool_name is required and must be a non-empty string"
                )

            # Optional field: tool_parameters (standardized name)
            tool_parameters = payload.get("tool_parameters")
            if tool_parameters is None:
                # Check for legacy tool_params field and warn about deprecation
                tool_parameters = payload.get("tool_params")
                if tool_parameters is not None:
                    self.logger.warning(
                        f"Deprecated field 'tool_params' found in payload. Please use 'tool_parameters' instead. "
                        f"Support for 'tool_params' will be removed in future versions."
                    )
                else:
                    # Set default empty dict if neither field is provided
                    tool_parameters = {}

            if tool_parameters is not None and not isinstance(tool_parameters, dict):
                validation_errors.append(
                    "tool_parameters must be a dictionary/object when provided"
                )

            # If any required fields are missing, reject the message
            if validation_errors:
                error_message = f"Invalid message payload at offset {offset}. Validation errors: {'; '.join(validation_errors)}. Payload: {payload}"
                self.logger.error(error_message)

                # Send enhanced error response if request_id is available
                request_id = payload.get("request_id")
                correlation_id = payload.get("correlation_id")
                if request_id:
                    validation_error = create_validation_error(
                        "payload", "; ".join(validation_errors), payload
                    )
                    await self.send_error_response_from_exception(
                        reply_topic=self.reply_topic,
                        exception=validation_error,
                        request_id=request_id,
                        correlation_id=correlation_id,
                        additional_context={
                            "offset": offset,
                            "validation_errors": validation_errors,
                        },
                    )

                commit_offset = True
                await self._commit_offset(topic_partition, offset, commit_offset)
                return

            # Log successfully extracted required parameters
            self.logger.info(
                f"✅ Required parameters extracted successfully - "
                f"mcp_id: {mcp_id}, user_id: {user_id}, tool_name: {tool_name}, "
                f"tool_parameters keys: {list(tool_parameters.keys())}"
            )

            # ===== PHASE 2: EXTRACT OPTIONAL PARAMETERS =====
            # Extract optional parameters with defaults
            correlation_id = payload.get("correlation_id")
            retries = payload.get("retries", settings.default_mcp_retries)
            transition_id = payload.get("transition_id")
            node_label = payload.get("node_label")

            self.logger.debug(
                f"Optional parameters extracted - correlation_id: {correlation_id}, "
                f"retries: {retries}"
            )

            # ===== PHASE 3: CREDENTIAL RETRIEVAL =====
            # Since all required parameters are now validated, we can proceed with credential retrieval
            self.logger.debug("Configuration refreshed before processing request")

            # OAuth credentials will be fetched by the MCP executor only if needed
            # based on the MCP config's oauth_details field
            oauth_credentials = None
            self.logger.debug(
                "OAuth credentials will be fetched by MCP executor if oauth_details are present"
            )

            # ===== PHASE 4: TOOL EXECUTION =====
            # Execute the tool using validated required parameters and optional parameters
            try:
                self.logger.info(
                    f"🚀 Executing tool '{tool_name}' for mcp_id='{mcp_id}', user_id='{user_id}'"
                )

                result = await self.mcp_executor.execute_tool(
                    # Required parameters (validated)
                    mcp_id=mcp_id,
                    user_id=user_id,
                    tool_name=tool_name,
                    tool_parameters=tool_parameters,
                    # Optional parameters
                    retries=retries,
                    correlation_id=correlation_id,
                    oauth_credentials=oauth_credentials,
                    node_label=node_label,
                    transition_id=transition_id,
                )

                self.logger.info(
                    f"✅ Successfully processed request offset={offset} for tool '{tool_name}' "
                    f"(mcp_id='{mcp_id}', user_id='{user_id}'). Result type: {type(result)}"
                )

                def parse_result(result):
                    """
                    Parse MCP result to check for error conditions.

                    Handles both structured error responses (JSON/dict) and plain text content.
                    Only attempts JSON parsing if the string looks like JSON (starts with { or [).
                    """
                    if not isinstance(result, list) or not result:
                        raise ValueError("Result must be a non-empty list.")

                    first_item = result[0]

                    if isinstance(first_item, str):
                        # Only try to parse as JSON if it looks like JSON
                        stripped = first_item.strip()
                        if stripped.startswith(("{", "[")):
                            try:
                                parsed = json.loads(first_item)
                                self.logger.debug(
                                    "Successfully parsed string result as JSON"
                                )
                            except json.JSONDecodeError as e:
                                self.logger.warning(
                                    f"String looks like JSON but failed to parse: {e}. "
                                    f"Treating as plain text content."
                                )
                                # Return a wrapper dict to indicate this is plain text content
                                parsed = {"content": first_item, "is_plain_text": True}
                        else:
                            # Plain text content - wrap in a dict for consistent handling
                            self.logger.debug(
                                "String result detected as plain text content"
                            )
                            parsed = {"content": first_item, "is_plain_text": True}
                    elif isinstance(first_item, dict):
                        parsed = first_item
                        self.logger.debug("Dictionary result detected")
                    elif isinstance(first_item, list):
                        # Handle case where result is a nested list structure
                        # This can happen when MCP tools return arrays of data
                        self.logger.debug(
                            "List result detected - treating as structured data"
                        )
                        parsed = {"content": first_item, "is_structured_list": True}
                    else:
                        raise TypeError(
                            f"Unexpected type in result list: {type(first_item)}"
                        )

                    return parsed

                parsed = parse_result(result)

                # Only check for error conditions if this is structured data (not plain text or structured list)
                if (
                    not parsed.get("is_plain_text", False)
                    and not parsed.get("is_structured_list", False)
                    and parsed.get("is_error") is True
                ):
                    raise InternalServerError(
                        f"MCP returned error status: {parsed.get('message')}"
                    )

                await self.send_result(result, payload.get("request_id"))
                commit_offset = True
            except InternalServerError as e:
                self.logger.error(
                    f"MCP Execution failed permanently for request at offset {offset} after retries: {e}"
                )
                # Send enhanced error response for MCP execution failures
                await self.send_error_response_from_exception(
                    reply_topic=self.reply_topic,
                    exception=e,
                    request_id=payload.get("request_id"),
                    correlation_id=payload.get("correlation_id"),
                    additional_context={"offset": offset, "phase": "mcp_execution"},
                )

                dead_letter_topic = "mcp_dead_letter_queue"
                dlq_payload = {
                    "original_topic": msg.topic,
                    "partition": msg.partition,
                    "offset": offset,
                    "payload": payload,
                    "error": str(e),
                }
                try:
                    await self.producer.send_and_wait(
                        dead_letter_topic, value=dlq_payload
                    )
                    self.logger.info(
                        f"Sent message to DLQ '{dead_letter_topic}': {dlq_payload}"
                    )
                except Exception as dlq_err:
                    self.logger.error(
                        f"Failed to send message to DLQ '{dead_letter_topic}': {dlq_err}",
                        exc_info=True,
                    )
                commit_offset = True
            except Exception as e:
                self.logger.error(
                    f"Unexpected error executing MCP tool at offset {offset}: {e}",
                    exc_info=True,
                )
                # Send error response for unexpected errors
                request_id = payload.get("request_id") if payload else None
                correlation_id = payload.get("correlation_id") if payload else None
                if request_id:
                    await self.send_error_response_from_exception(
                        reply_topic=self.reply_topic,
                        exception=e,
                        request_id=request_id,
                        correlation_id=correlation_id,
                        additional_context={
                            "offset": offset,
                            "phase": "tool_execution",
                        },
                    )
                commit_offset = False  # DO NOT COMMIT - let Kafka redeliver

        except json.JSONDecodeError as json_error:
            self.logger.error(
                f"Failed to decode JSON message at offset {offset}: {msg.value.decode('utf-8', errors='ignore')}"
            )
            # Send error response for JSON decode errors
            # Try to extract request_id from raw message if possible
            request_id = None
            try:
                # Attempt to extract request_id from malformed JSON
                raw_message = msg.value.decode("utf-8", errors="ignore")
                if '"request_id"' in raw_message:
                    # Simple regex-like extraction (not perfect but better than nothing)
                    import re

                    match = re.search(r'"request_id"\s*:\s*"([^"]+)"', raw_message)
                    if match:
                        request_id = match.group(1)
            except:
                pass

            if request_id:
                await self.send_error_response_from_exception(
                    reply_topic=self.reply_topic,
                    exception=json_error,
                    request_id=request_id,
                    additional_context={
                        "offset": offset,
                        "raw_message_preview": msg.value.decode(
                            "utf-8", errors="ignore"
                        )[:200],
                    },
                )
            commit_offset = True
        except Exception as e:
            self.logger.error(
                f"Unexpected error processing message at offset {offset}: {e}",
                exc_info=True,
            )
            # Send error response for unexpected processing errors
            request_id = payload.get("request_id") if payload else None
            correlation_id = payload.get("correlation_id") if payload else None
            if request_id:
                await self.send_error_response_from_exception(
                    reply_topic=self.reply_topic,
                    exception=e,
                    request_id=request_id,
                    correlation_id=correlation_id,
                    additional_context={
                        "offset": offset,
                        "phase": "message_processing",
                    },
                )
            commit_offset = False  # DO NOT COMMIT - let Kafka redeliver
        finally:
            # Commit the offset if needed
            await self._commit_offset(topic_partition, offset, commit_offset)

    async def _commit_offset(self, topic_partition, offset, commit_offset):
        """Helper method to commit Kafka offset with enhanced error handling."""
        if commit_offset:
            try:
                self.logger.debug(
                    f"Committing offset {offset + 1} for partition {topic_partition}"
                )
                await self.consumer.commit({topic_partition: offset + 1})
                self.logger.debug(f"Successfully committed offset {offset + 1}")
            except KafkaError as kafka_error:
                self.logger.error(
                    f"Kafka error committing offset {offset + 1} for partition {topic_partition}: {kafka_error}",
                    exc_info=True,
                )
                # Send error notification for commit failures
                await self._send_service_status_update(
                    "warning",
                    f"Failed to commit Kafka offset: {str(kafka_error)}",
                    error_details={
                        "error_type": "kafka_commit_error",
                        "topic_partition": str(topic_partition),
                        "offset": offset + 1,
                        "exception_type": type(kafka_error).__name__,
                    },
                )
            except Exception as e:
                self.logger.error(
                    f"Unexpected error committing offset {offset + 1} for partition {topic_partition}: {e}",
                    exc_info=True,
                )
                # Send error notification for unexpected commit failures
                await self._send_service_status_update(
                    "warning",
                    f"Unexpected error committing offset: {str(e)}",
                    error_details={
                        "error_type": "unexpected_commit_error",
                        "topic_partition": str(topic_partition),
                        "offset": offset + 1,
                        "exception_type": type(e).__name__,
                    },
                )

    async def send_result(self, result_data: Any, request_id: Optional[str] = None):
        """Send result with enhanced error handling and monitoring."""
        try:
            from datetime import datetime, timezone

            message = {
                "request_id": request_id,
                "result": result_data,
                "mcp_status": "success",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
            self.logger.info(f"Sending result to topic '{self.reply_topic}': {message}")
            await self.producer.send_and_wait(self.reply_topic, value=message)
            self.logger.debug(f"Successfully sent result for request_id: {request_id}")

        except KafkaError as kafka_error:
            self.logger.error(
                f"Kafka error sending result to topic '{self.reply_topic}': {kafka_error}",
                exc_info=True,
            )
            # Send error notification for result send failures
            await self._send_service_status_update(
                "warning",
                f"Failed to send result to Kafka: {str(kafka_error)}",
                error_details={
                    "error_type": "kafka_result_send_error",
                    "topic": self.reply_topic,
                    "request_id": request_id,
                    "exception_type": type(kafka_error).__name__,
                },
            )
        except Exception as e:
            self.logger.error(f"Unexpected error sending result: {e}", exc_info=True)
            # Send error notification for unexpected result send failures
            await self._send_service_status_update(
                "warning",
                f"Unexpected error sending result: {str(e)}",
                error_details={
                    "error_type": "unexpected_result_send_error",
                    "topic": self.reply_topic,
                    "request_id": request_id,
                    "exception_type": type(e).__name__,
                },
            )
