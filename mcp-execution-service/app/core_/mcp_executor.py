# mcp_executor.py

# CRITICAL: Load .env file BEFORE any MCP imports
# The MCP library reads MCP_TIMEOUT at import time, so this MUST be first
import os
from dotenv import load_dotenv

# Load .env file to make MCP_TIMEOUT available before MCP imports
load_dotenv()

# Verify MCP_TIMEOUT is loaded and set fallback if needed
if not os.environ.get("MCP_TIMEOUT"):
    os.environ["MCP_TIMEOUT"] = "900"  # 15 minutes fallback

# Now safe to import other modules
import asyncio
import datetime
import json
import logging
import aiohttp
from typing import Any, Optional, List, Dict, Union
from app.config.config import settings  # type: ignore
from werkzeug.exceptions import InternalServerError  # type: ignore
from app.core_.client import MCPClient, create_authenticated_client
from app.core_.oauth_client_factory import (
    create_oauth_aware_client,
    create_legacy_compatible_client,
    should_use_oauth_discovery,
)
from app.schemas.client import AuthenticationError, AuthenticationError  # type: ignore
from app.schemas.client import ConnectionConfig  # type: ignore
from app.core_.exceptions import (
    MCPConfigNotFoundError,
    MCPConfigInvalidError,
    ContainerCreationError,
    ContainerExecutionError,
    SSHConnectionError,
    MCPServerUnreachableError,
    MCPAuthenticationError,
    MCPToolExecutionError,
    MCPHTTPMethodError,
    CredentialRetrievalError,
    ErrorCategory,
    ErrorCode,
    MCPExecutorError,
)
from app.core_.error_handler import (
    format_kafka_error_response,
    create_infrastructure_error,
    classify_exception,
)
from app.core_.metrics import (
    ExecutionType,
    log_execution_start,
    log_execution_success,
    log_execution_failure,
    metrics_logger,
)
from app.services.container_client import ContainerManagementClient  # type: ignore
from app.config.config import settings


class MCPExecutor:
    """
    Simplified MCP executor with priority-based execution logic.

    Priority order:
    1. streamable-http (highest priority)
    2. stdio (second priority)
    3. sse (lowest priority)
    """

    def __init__(self, producer, logger: Optional[logging.Logger] = None):
        """
        Initializes the MCPExecutor.

        Args:
            producer: Kafka producer for sending results
            logger: An optional logger instance. If None, a default logger is created.
        """
        self.logger = logger or logging.getLogger(__name__)
        self.logger.info("MCPExecutor initialized with simplified logic.")
        self.producer = producer
        self.logging_topic = "workflow-responses"

        # Initialize MCP configs cache
        self.mcp_configs = {}  # Cache for MCP configurations

        # Initialize credential service if available
        try:
            from app.services.credential_service import CredentialService

            self.credential_service = CredentialService()
        except ImportError:
            self.credential_service = None
            self.logger.warning("Credential service not available")

    async def fetch_mcp_config(self, mcp_id: str) -> Dict[str, Any]:
        """
        Simple MCP config fetcher - direct HTTP GET request.

        Args:
            mcp_id: MCP server identifier

        Returns:
            MCP configuration dictionary

        Raises:
            MCPConfigNotFoundError: If configuration cannot be found
            MCPConfigInvalidError: If configuration is invalid
        """
        # Use hardcoded URL for now - will be configurable later
        url = f"{settings.api_base_url}/api/v1/mcps/orchestration/{mcp_id}"

        # Start timing for metrics
        start_time = asyncio.get_event_loop().time()

        try:
            self.logger.info(f"Fetching MCP configuration from: {url}")

            headers = {"X-Server-Auth-Key": settings.server_auth_key}

            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(
                    total=900
                )  # 15 minutes timeout for long-running operations
            ) as session:
                async with session.get(url, headers=headers) as response:
                    duration_ms = (asyncio.get_event_loop().time() - start_time) * 1000

                    if response.status == 200:
                        config = await response.json()
                        self.logger.info(
                            f"Successfully fetched MCP config for {mcp_id}"
                        )

                        # Log successful config fetch
                        metrics_logger.log_config_fetch(
                            mcp_id, duration_ms, success=True
                        )

                        # Validate config structure
                        if not isinstance(config, dict):
                            raise MCPConfigInvalidError(
                                mcp_id,
                                "Configuration is not a valid dictionary",
                                {"config_type": type(config).__name__},
                            )

                        return config

                    elif response.status == 404:
                        metrics_logger.log_config_fetch(
                            mcp_id, duration_ms, success=False, error_type="not_found"
                        )
                        raise MCPConfigNotFoundError(
                            mcp_id, {"status_code": response.status, "url": url}
                        )
                    else:
                        response_text = await response.text()
                        metrics_logger.log_config_fetch(
                            mcp_id, duration_ms, success=False, error_type="api_error"
                        )
                        raise MCPConfigInvalidError(
                            mcp_id,
                            f"API returned status {response.status}",
                            {
                                "status_code": response.status,
                                "response": response_text[:200],
                            },
                        )

        except (MCPConfigNotFoundError, MCPConfigInvalidError):
            # Re-raise our custom exceptions
            raise

        except Exception as e:
            duration_ms = (asyncio.get_event_loop().time() - start_time) * 1000
            metrics_logger.log_config_fetch(
                mcp_id, duration_ms, success=False, error_type="unexpected_error"
            )
            error_msg = f"Error fetching MCP config for {mcp_id}: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            raise MCPConfigInvalidError(
                mcp_id, f"Unexpected error: {str(e)}", {"error_type": type(e).__name__}
            )

    async def _execute_url_tool(
        self,
        server_url: str,
        tool_name: str,
        tool_parameters: dict,
        retries: int = 3,
        correlation_id: Optional[str] = None,
        oauth_credentials: Optional[Dict[str, Any]] = None,
        user_id: Optional[str] = None,
        mcp_id: Optional[str] = None,
        node_label: Optional[str] = None,
        transition_id: Optional[str] = None,
    ) -> List[Union[Dict[str, Any], str]]:
        """
        Execute tool using URL-based connection (SSE or streamable-http).

        Args:
            server_url: URL to the MCP server
            tool_name: Name of the tool to execute
            tool_parameters: Parameters for the tool
            retries: Number of retry attempts
            correlation_id: Optional correlation ID for tracking
            oauth_credentials: Pre-fetched OAuth credentials
            user_id: User identifier for credential retrieval
            mcp_id: MCP server identifier for credential retrieval

        Returns:
            List of results from tool execution

        Raises:
            MCPServerUnreachableError: If server cannot be reached
            MCPAuthenticationError: If authentication fails
            MCPToolExecutionError: If tool execution fails
        """
        self.logger.info(f"🌐 Executing URL-based tool '{tool_name}' on {server_url}")

        # Start execution metrics
        start_time = log_execution_start(
            ExecutionType.URL, mcp_id or "unknown", user_id or "unknown", tool_name
        )

        # Create OAuth-aware client based on MCP configuration
        client_instance = None

        # Fetch MCP config for this execution
        mcp_config = {}
        if mcp_id:
            try:
                self.logger.debug(f"Fetching MCP config for mcp_id: {mcp_id}")
                mcp_config_response = await self.fetch_mcp_config(mcp_id)
                # Extract the actual MCP config from the response
                # Response structure: {"success": true, "message": "...", "mcp": {...}}
                if (
                    isinstance(mcp_config_response, dict)
                    and "mcp" in mcp_config_response
                ):
                    mcp_config = mcp_config_response["mcp"]
                    self.logger.debug(f"Successfully extracted MCP config for {mcp_id}")
                    oauth_details = mcp_config.get("oauth_details")
                    if oauth_details:
                        self.logger.info(
                            f"OAuth details found for {mcp_id}: provider={oauth_details.get('provider')}, tool_name={oauth_details.get('tool_name')}"
                        )
                    else:
                        self.logger.debug(f"No OAuth details found for {mcp_id}")
                else:
                    self.logger.warning(
                        f"Unexpected MCP config response structure for {mcp_id}"
                    )
                    mcp_config = mcp_config_response
            except Exception as e:
                self.logger.warning(f"Failed to fetch MCP config for {mcp_id}: {e}")
                mcp_config = {}

        # Check if we should use OAuth discovery flow
        if should_use_oauth_discovery(mcp_config, user_id):
            self.logger.info(
                f"Creating OAuth-aware MCP client with discovery flow for {mcp_id}"
            )
            # Create connection config with extended timeouts
            connection_config = ConnectionConfig(
                max_retries=1,
                enable_health_check=False,
                default_timeout=900.0,  # 15 minutes
                long_running_timeout=900.0,  # 15 minutes
                read_timeout=900.0,  # 15 minutes read timeout
                connection_timeout=30.0,  # Connection establishment timeout
            )
            self.logger.info(
                f"Using extended timeout configuration: sse_read_timeout={connection_config.read_timeout}s, connection_timeout={connection_config.connection_timeout}s"
            )
            client_instance = create_oauth_aware_client(
                server_url=server_url,
                user_id=user_id,
                mcp_config=mcp_config,
                credential_service=self.credential_service,
                connection_config=connection_config,
                timeout=900.0,  # 15 minutes timeout
            )
        else:
            # Fallback to legacy flow for backward compatibility
            self.logger.info(f"Using legacy authentication flow for {mcp_id}")
            # Create connection config with extended timeouts for legacy client
            connection_config = ConnectionConfig(
                max_retries=1,
                enable_health_check=False,
                default_timeout=900.0,  # 15 minutes
                long_running_timeout=900.0,  # 15 minutes
                read_timeout=900.0,  # 15 minutes read timeout
                connection_timeout=30.0,  # Connection establishment timeout
            )
            client_instance = create_legacy_compatible_client(
                server_url=server_url,
                oauth_credentials=oauth_credentials,
                user_id=user_id,
                mcp_config=mcp_config,
                connection_config=connection_config,
                timeout=900.0,  # 15 minutes timeout
            )

        # Prepare headers for correlation tracking
        kafka_headers = None
        if correlation_id:
            kafka_headers = [("correlationId", correlation_id.encode("utf-8"))]

        results = []
        last_error = None

        try:
            for attempt in range(retries):
                try:
                    connection_info = f"URL server at {server_url}"

                    # Execute with MCP client with overall timeout wrapper
                    try:
                        # Add overall timeout of 20 minutes (longer than transport timeout)
                        async with asyncio.timeout(1200):  # 20 minutes
                            async with client_instance as client:
                                self.logger.info(
                                    f"Connected to {connection_info} (Attempt {attempt + 1}/{retries})"
                                )
                                result_info = {
                                    "result": "Connected to MCP server",
                                    "message": "Connected to MCP server",
                                    "status": "connected",
                                    "node_label": node_label,
                                    "transition_id": transition_id,
                                    "workflow_status": "running",
                                }
                                self.logger.info(
                                    f"Sending result to topic '{self.logging_topic}': {result_info}"
                                )
                                await self.producer.send(
                                    self.logging_topic,
                                    result_info,
                                    headers=kafka_headers,
                                )
                                result_info["result"] = "Processing request..."
                                await self.producer.send(
                                    self.logging_topic,
                                    result_info,
                                    headers=kafka_headers,
                                )

                                self.logger.info(f"Executing tool: {tool_name}")
                                self.logger.info(f"Tool parameters: {tool_parameters}")

                                self.logger.info("About to call MCP tool...")
                                # Add read timeout to the tool call itself (15 minutes)
                                read_timeout = datetime.timedelta(
                                    seconds=900
                                )  # 15 minutes
                                self.logger.info(
                                    f"Using tool call read_timeout: {read_timeout}"
                                )
                                result = await client.call_tool(
                                    tool_name,
                                    tool_parameters,
                                    read_timeout_seconds=read_timeout,
                                )
                                self.logger.info("MCP tool call completed!")

                                self.logger.info(f"MCP result raw: {result}")
                                self.logger.info(f"MCP result type: {type(result)}")

                        # Handle different result structures
                        # Case 1: Direct result object with content attribute
                        # Case 2: Nested result with {'result': {'content': [...]}} structure
                        # Case 3: Dictionary result with nested structure

                        actual_result = result

                        # Check if result is a dict with nested 'result' key
                        if isinstance(result, dict) and "result" in result:
                            self.logger.debug(
                                "Found nested result structure, extracting inner result"
                            )
                            actual_result = result["result"]

                        # Check for error conditions first
                        if (
                            actual_result
                            and hasattr(actual_result, "isError")
                            and isinstance(actual_result.isError, list)
                        ):
                            if actual_result.isError:
                                error_content = getattr(
                                    actual_result,
                                    "content",
                                    "No error details provided.",
                                )
                                self.logger.error(
                                    f"MCP server returned explicit error list: {error_content}"
                                )
                                last_error = {"error": f"MCP Error: {error_content}"}
                                continue

                        # Extract content from the actual result
                        content_list = None

                        # Try to get content from object attribute
                        if hasattr(actual_result, "content") and isinstance(
                            actual_result.content, list
                        ):
                            content_list = actual_result.content
                            self.logger.debug("Found content as object attribute")
                        # Try to get content from dictionary key
                        elif (
                            isinstance(actual_result, dict)
                            and "content" in actual_result
                            and isinstance(actual_result["content"], list)
                        ):
                            content_list = actual_result["content"]
                            self.logger.debug("Found content as dictionary key")

                        if content_list:
                            # Extract text from content items
                            text_contents = []
                            for content_item in content_list:
                                # Handle object with text attribute
                                if hasattr(content_item, "text") and isinstance(
                                    content_item.text, str
                                ):
                                    text_contents.append(content_item.text)
                                # Handle dictionary with text key
                                elif (
                                    isinstance(content_item, dict)
                                    and "text" in content_item
                                    and isinstance(content_item["text"], str)
                                ):
                                    text_contents.append(content_item["text"])

                            if not text_contents:
                                self.logger.warning(
                                    f"MCP result for {tool_name} had content, but no text fields."
                                )
                                results.append("[WARNING] No text results returned")
                                return results

                            extracted_data = []
                            for text in text_contents:
                                try:
                                    parsed_json = json.loads(text)

                                    if isinstance(
                                        parsed_json, dict
                                    ) and parsed_json.get("is_error"):
                                        error_msg = parsed_json.get(
                                            "message",
                                            "Unknown error from tool execution.",
                                        )
                                        self.logger.error(
                                            f"Tool execution indicated error in JSON payload: {error_msg}"
                                        )
                                        last_error = {
                                            "error": f"Tool Error: {error_msg}"
                                        }
                                        continue

                                    extracted_data.append(parsed_json)
                                except json.JSONDecodeError:
                                    self.logger.warning(
                                        f"Content item is not valid JSON, returning as raw text: {text[:100]}..."
                                    )
                                    extracted_data.append(text)
                                except Exception as parse_exc:
                                    self.logger.error(
                                        f"Unexpected error processing text content: {parse_exc}",
                                        exc_info=True,
                                    )
                                    extracted_data.append(
                                        f"[ERROR] Failed to process content: {text[:100]}..."
                                    )

                            if extracted_data:
                                results.extend(extracted_data)
                                return results
                            else:
                                self.logger.warning(
                                    "No valid data extracted from any text content, though text fields were present."
                                )
                                results.extend(text_contents)
                                return results
                        else:
                            # Fallback: Handle direct string results or other formats
                            self.logger.info(
                                f"MCP result for {tool_name} doesn't have expected content structure. "
                                f"Result type: {type(result)}, Actual result type: {type(actual_result)}, "
                                f"Attempting to process as direct result..."
                            )

                            # Try to handle direct string result
                            if isinstance(actual_result, str):
                                self.logger.info("Processing direct string result")
                                results.append(actual_result)
                                return results

                            # Try to handle list of strings
                            elif isinstance(actual_result, list):
                                self.logger.info("Processing list result")
                                for item in actual_result:
                                    if isinstance(item, str):
                                        results.append(item)
                                    elif isinstance(item, dict):
                                        # Try to extract text from dict
                                        if "text" in item:
                                            results.append(item["text"])
                                        else:
                                            results.append(str(item))
                                    else:
                                        results.append(str(item))
                                if results:
                                    return results

                            # Try to handle dict result
                            elif isinstance(actual_result, dict):
                                self.logger.info("Processing dict result")
                                # Look for common text fields
                                for text_field in [
                                    "text",
                                    "content",
                                    "data",
                                    "result",
                                    "message",
                                ]:
                                    if text_field in actual_result:
                                        results.append(str(actual_result[text_field]))
                                        return results
                                # If no text fields found, convert whole dict to string
                                results.append(str(actual_result))
                                return results

                            # Last resort: convert to string
                            else:
                                self.logger.info(
                                    "Converting result to string as last resort"
                                )
                                results.append(str(actual_result))
                                return results

                    except asyncio.TimeoutError:
                        self.logger.error(
                            f"Tool execution timed out after 20 minutes on attempt {attempt + 1}"
                        )
                        last_error = {
                            "error": "Tool execution timed out after 20 minutes"
                        }
                        if attempt < retries - 1:
                            self.logger.info(f"Retrying {tool_name} after timeout...")
                            await asyncio.sleep(2 ** (attempt + 1))
                            continue
                        else:
                            break

                except AuthenticationError as auth_error:
                    self.logger.error(
                        f"Authentication failed on attempt {attempt+1}/{retries}: {str(auth_error)}"
                    )

                    # Log authentication failure metrics
                    if user_id and mcp_id:
                        metrics_logger.log_authentication_failure(
                            user_id, mcp_id, "authentication_error", "validate"
                        )

                    # Log authentication failure details for debugging
                    if oauth_credentials:
                        composite_key = (
                            f"{user_id}_{mcp_id}_{tool_name}"
                            if user_id and mcp_id
                            else "unknown"
                        )
                        self.logger.error(
                            f"Authentication failure for composite key: {composite_key}"
                        )

                    # Log execution failure and raise custom exception
                    log_execution_failure(
                        ExecutionType.URL,
                        start_time,
                        "authentication_error",
                        mcp_id or "unknown",
                        user_id or "unknown",
                        tool_name,
                        attempt + 1,
                    )

                    raise MCPAuthenticationError(
                        server_url,
                        str(auth_error),
                        {
                            "composite_key": (
                                f"{user_id}_{mcp_id}_{tool_name}"
                                if user_id and mcp_id
                                else "unknown"
                            )
                        },
                    )

                except InternalServerError as ise:
                    self.logger.error(
                        f"Tool call failed on attempt {attempt+1}/{retries} due to InternalServerError: {str(ise)}"
                    )
                    last_error = {"error": str(ise)}
                    if attempt < retries - 1:
                        self.logger.info(
                            f"Retrying {tool_name} after InternalServerError..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))

                except (ConnectionError, aiohttp.ClientError) as conn_error:
                    self.logger.error(
                        f"Connection error on attempt {attempt+1}/{retries} for {tool_name} on {server_url}: {str(conn_error)}"
                    )

                    # Create specific connection error
                    conn_error_obj = MCPServerUnreachableError(
                        server_url,
                        str(conn_error),
                        {
                            "tool_name": tool_name,
                            "attempt": attempt + 1,
                            "error_type": type(conn_error).__name__,
                        },
                    )

                    result_info = {
                        "result": f"Connection failed, retrying...",
                        "status": "disconnected",
                        "node_label": node_label,
                        "transition_id": transition_id,
                        "workflow_status": "running",
                    }
                    await self.producer.send(
                        self.logging_topic, result_info, headers=kafka_headers
                    )

                    last_error = {"error": str(conn_error_obj)}
                    if attempt < retries - 1:
                        self.logger.info(
                            f"Retrying {tool_name} after connection error..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))
                    else:
                        # Final attempt failed due to connection error
                        raise conn_error_obj

                except Exception as e:
                    self.logger.error(
                        f"Tool call failed on attempt {attempt+1}/{retries} for {tool_name} on {server_url} with unexpected error: {str(e)}",
                        exc_info=True,
                    )

                    # Create categorized error based on exception type
                    categorized_error = MCPToolExecutionError(
                        tool_name,
                        str(e),
                        {
                            "server_url": server_url,
                            "attempt": attempt + 1,
                            "error_type": type(e).__name__,
                            "execution_type": "url",
                        },
                    )

                    result_info = {
                        "result": f"Tool call failed with unexpected error: {str(e)}, Retrying...",
                        "status": "connected",
                        "node_label": node_label,
                        "transition_id": transition_id,
                        "workflow_status": "running",
                    }
                    await self.producer.send(
                        self.logging_topic, result_info, headers=kafka_headers
                    )

                    last_error = {"error": str(categorized_error)}
                    if attempt < retries - 1:
                        self.logger.info(
                            f"Retrying {tool_name} after unexpected error..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))

            if last_error:
                self.logger.error(
                    f"Execution failed for {tool_name} after {retries} attempts with error: {last_error}"
                )
                result_info = {
                    "result": f"Execution failed after {retries} attempts with error: {last_error}",
                    "status": "disconnected",
                    "node_label": node_label,
                    "transition_id": transition_id,
                    "workflow_status": "running",
                }
                await self.producer.send(
                    self.logging_topic, result_info, headers=kafka_headers
                )

                # Create final execution failure error
                raise MCPToolExecutionError(
                    tool_name,
                    f"Failed to execute tool after {retries} attempts: {last_error}",
                    {
                        "server_url": server_url,
                        "retries": retries,
                        "execution_type": "url",
                        "last_error": str(last_error),
                    },
                )

            self.logger.error(
                f"Execution loop for {tool_name} completed without returning or raising an exception after {retries} retries."
            )
            result_info = {
                "result": f"Execution returned no results after {retries} attempts.",
                "status": "disconnected",
                "node_label": node_label,
                "transition_id": transition_id,
                "workflow_status": "running",
            }
            await self.producer.send(
                self.logging_topic, result_info, headers=kafka_headers
            )

            # Create no results error
            raise MCPToolExecutionError(
                tool_name,
                f"Tool execution completed but returned no results after {retries} attempts",
                {"server_url": server_url, "retries": retries, "execution_type": "url"},
            )

        except (
            MCPAuthenticationError,
            MCPServerUnreachableError,
            MCPToolExecutionError,
            MCPHTTPMethodError,
            MCPExecutorError,
        ):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            self.logger.error(
                f"Unexpected error executing MCP tool: {e}", exc_info=True
            )
            # Convert unexpected errors to categorized exceptions
            raise MCPToolExecutionError(
                tool_name,
                f"Unexpected error during URL execution: {str(e)}",
                {
                    "server_url": server_url,
                    "execution_type": "url",
                    "error_type": type(e).__name__,
                },
            ) from e

    async def _execute_container_tool(
        self,
        tool_name: str,
        tool_parameters: dict,
        retries: int = 3,
        correlation_id: Optional[str] = None,
        oauth_credentials: Optional[Dict[str, Any]] = None,
        user_id: str = None,
        mcp_id: str = None,
        node_label: Optional[str] = None,
        transition_id: Optional[str] = None,
    ) -> List[Union[Dict[str, Any], str]]:
        """
        Simplified container execution method.

        Args:
            tool_name: Name of the tool to execute
            tool_parameters: Parameters for the tool
            retries: Number of retry attempts
            correlation_id: Optional correlation ID for tracking
            oauth_credentials: Pre-fetched OAuth credentials
            user_id: User identifier for credential retrieval
            mcp_id: MCP server identifier for credential retrieval

        Returns:
            List of results from tool execution

        Raises:
            ContainerCreationError: If container creation fails
            ContainerExecutionError: If container execution fails
            SSHConnectionError: If SSH connection fails
        """
        self.logger.info(
            f"🐳 Executing container-based tool '{tool_name}' for mcp_id='{mcp_id}', user_id='{user_id}'"
        )

        # Start execution metrics
        start_time = log_execution_start(
            ExecutionType.CONTAINER,
            mcp_id or "unknown",
            user_id or "unknown",
            tool_name,
        )

        container_client = ContainerManagementClient()
        container_id = None

        try:
            # Step 1: Create container
            self.logger.info(
                f"Creating container for user_id: {user_id}, mcp_id: {mcp_id}"
            )

            success, message, container_id = await container_client.create_container(
                mcp_id=mcp_id,
                user_id=user_id,
                container_type="stdio",
                env=None,
            )

            if not success:
                error_msg = f"Failed to create container: {message}"
                self.logger.error(error_msg)

                # Log container creation failure metrics
                metrics_logger.log_container_lifecycle(
                    "create",
                    f"{mcp_id}_{user_id}",
                    0,
                    success=False,
                    error_type="creation_failed",
                )

                # Log execution failure and raise custom exception
                log_execution_failure(
                    ExecutionType.CONTAINER,
                    start_time,
                    "container_creation_failed",
                    mcp_id or "unknown",
                    user_id or "unknown",
                    tool_name,
                    1,
                )

                raise ContainerCreationError(
                    mcp_id or "unknown",
                    user_id or "unknown",
                    message,
                    {"container_type": "stdio"},
                )

            self.logger.info(f"✅ Successfully created container: {container_id}")

            # Step 2: Wait for container startup
            startup_wait_time = 10  # seconds
            self.logger.info(
                f"⏳ Waiting {startup_wait_time} seconds for container startup..."
            )
            await asyncio.sleep(startup_wait_time)

            # Step 3: Execute tool via container
            actual_container_id = f"{mcp_id}_{user_id}"  # Standard naming convention
            self.logger.info(f"🔧 Using container name: {actual_container_id}")

            # Create SSH Docker client for container execution
            client_instance = MCPClient(
                docker_image=actual_container_id,
                connection_type="ssh_docker",
                container_command=None,  # Let it auto-detect
                use_fallback_ssh=True,
            )

            # Prepare headers for correlation tracking
            kafka_headers = None
            if correlation_id:
                kafka_headers = [("correlationId", correlation_id.encode("utf-8"))]

            results = []
            last_error = None

            # Execute with retries
            for attempt in range(retries):
                try:
                    async with client_instance as client:
                        self.logger.info(
                            f"🔗 Connected to container (Attempt {attempt + 1}/{retries})"
                        )

                        # Send status update
                        result_info = {
                            "result": "Connected to container MCP server",
                            "status": "connected",
                            "node_label": node_label,
                            "transition_id": transition_id,
                            "workflow_status": "running",
                        }
                        await self.producer.send(
                            self.logging_topic, result_info, headers=kafka_headers
                        )

                        # Execute tool
                        self.logger.info(f"🚀 Executing tool: {tool_name}")
                        result = await client.call_tool(tool_name, tool_parameters)

                        self.logger.info(f"✅ Tool execution completed successfully")

                        # Simple result processing - just return the result
                        if result:
                            results.append(result)
                            return results
                        else:
                            results.append(
                                "Tool executed successfully but returned no result"
                            )
                            return results

                except SSHConnectionError as ssh_error:
                    self.logger.error(
                        f"❌ SSH connection failed on attempt {attempt+1}/{retries}: {str(ssh_error)}"
                    )
                    last_error = {"error": str(ssh_error)}

                    if attempt < retries - 1:
                        self.logger.info(
                            f"🔄 Retrying SSH connection in {2 ** (attempt + 1)} seconds..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))
                    else:
                        # Final attempt failed due to SSH error
                        raise ssh_error

                except Exception as e:
                    self.logger.error(
                        f"❌ Tool execution failed on attempt {attempt+1}/{retries}: {str(e)}",
                        exc_info=True,
                    )

                    # Create categorized container execution error
                    categorized_error = ContainerExecutionError(
                        f"{mcp_id}_{user_id}",
                        str(e),
                        {
                            "tool_name": tool_name,
                            "attempt": attempt + 1,
                            "error_type": type(e).__name__,
                            "execution_type": "container",
                        },
                    )

                    last_error = {"error": str(categorized_error)}

                    if attempt < retries - 1:
                        self.logger.info(
                            f"🔄 Retrying in {2 ** (attempt + 1)} seconds..."
                        )
                        await asyncio.sleep(2 ** (attempt + 1))

            # If we get here, all retries failed
            if last_error:
                self.logger.error(
                    f"❌ Container execution failed after {retries} attempts: {last_error}"
                )

                # Create final container execution failure error
                raise ContainerExecutionError(
                    f"{mcp_id}_{user_id}",
                    f"Failed to execute tool after {retries} attempts: {last_error}",
                    {
                        "tool_name": tool_name,
                        "retries": retries,
                        "execution_type": "container",
                        "last_error": str(last_error),
                    },
                )

        except (
            ContainerCreationError,
            ContainerExecutionError,
            SSHConnectionError,
            MCPExecutorError,
        ):
            # Re-raise our custom exceptions
            raise
        except Exception as e:
            self.logger.error(f"❌ Container lifecycle error: {e}", exc_info=True)
            # Convert unexpected errors to categorized exceptions
            raise ContainerExecutionError(
                f"{mcp_id}_{user_id}",
                f"Unexpected container lifecycle error: {str(e)}",
                {
                    "tool_name": tool_name,
                    "execution_type": "container",
                    "error_type": type(e).__name__,
                },
            ) from e
        finally:
            # Step 4: Cleanup container
            if container_id:
                try:
                    self.logger.info(
                        f"🗑️ Stopping and Deleting container: {container_id}"
                    )
                    # Note: delete_container doesn't have enhanced error handling yet
                    # but we'll catch any errors and log them appropriately
                    await container_client.delete_container(container_id)

                    self.logger.info(f"✅ Container cleanup completed")
                except ContainerExecutionError as cleanup_error:
                    # Log container cleanup errors but don't re-raise them
                    # as they shouldn't affect the main execution result
                    self.logger.error(f"⚠️ Container cleanup failed: {cleanup_error}")
                    self.logger.error(
                        f"Container cleanup error details: {cleanup_error.details}"
                    )
                except Exception as cleanup_error:
                    self.logger.error(
                        f"⚠️ Container cleanup failed with unexpected error: {cleanup_error}"
                    )
                    # Create a categorized error for logging but don't raise it
                    categorized_cleanup_error = ContainerExecutionError(
                        container_id,
                        f"Container cleanup failed: {str(cleanup_error)}",
                        {
                            "operation": "cleanup",
                            "error_type": type(cleanup_error).__name__,
                            "mcp_id": mcp_id,
                            "user_id": user_id,
                        },
                    )
                    self.logger.error(
                        f"Categorized cleanup error: {categorized_cleanup_error}"
                    )

    async def execute_tool(
        self,
        mcp_id: str,
        user_id: str,
        tool_name: str,
        tool_parameters: dict,
        retries: int = 3,
        correlation_id: Optional[str] = None,
        oauth_credentials: Optional[Dict[str, Any]] = None,
        node_label: Optional[str] = None,
        transition_id: Optional[str] = None,
    ) -> List[Union[Dict[str, Any], str]]:
        """
        Simplified MCP tool execution with priority-based config selection.

        Priority order:
        1. streamable-http (highest priority)
        2. stdio (second priority)
        3. sse (lowest priority)

        Args:
            mcp_id: MCP server identifier (required)
            user_id: User identifier (required)
            tool_name: Name of the tool to execute (required)
            tool_parameters: Parameters for the tool (required)
            retries: Number of retry attempts
            correlation_id: Optional correlation ID for tracking
            oauth_credentials: Pre-fetched OAuth credentials

        Returns:
            List of results from tool execution

        Raises:
            ValueError: If MCP config is invalid or no supported config found
            InternalServerError: If tool execution fails after retries
        """
        self.logger.info(
            f"🚀 execute_tool called - mcp_id: {mcp_id}, user_id: {user_id}, tool: {tool_name}"
        )

        try:
            # Step 1: Fetch MCP config
            config = await self.fetch_mcp_config(mcp_id)
            config_array = config["mcp"]["config"]  # Array of config objects

            # Step 2: Priority-based selection logic
            selected_config = None
            execution_type = None

            # Priority 1: streamable-http (highest priority)
            for cfg in config_array:
                if cfg.get("type") == "streamable-http":
                    selected_config = cfg
                    execution_type = "url"
                    self.logger.info("🥇 Selected streamable-http (highest priority)")
                    break

            # Priority 2: stdio (second priority)
            if not selected_config:
                for cfg in config_array:
                    if cfg.get("type") == "stdio":
                        selected_config = cfg
                        execution_type = "container"
                        self.logger.info("🥈 Selected stdio (second priority)")
                        break

            # Priority 3: sse (lowest priority)
            if not selected_config:
                for cfg in config_array:
                    if cfg.get("type") == "sse":
                        selected_config = cfg
                        execution_type = "url"
                        self.logger.info("🥉 Selected sse (lowest priority)")
                        break

            # Step 3: Execute based on selected type
            if execution_type == "url":
                config_url = selected_config["url"]
                self.logger.info(f"🌐 Using URL execution with: {config_url}")
                result = await self._execute_url_tool(
                    server_url=config_url,
                    tool_name=tool_name,
                    tool_parameters=tool_parameters,
                    retries=retries,
                    correlation_id=correlation_id,
                    oauth_credentials=oauth_credentials,
                    user_id=user_id,
                    mcp_id=mcp_id,
                    node_label=node_label,
                    transition_id=transition_id,
                )
            elif execution_type == "container":
                self.logger.info(f"🐳 Using container execution")
                result = await self._execute_container_tool(
                    tool_name=tool_name,
                    tool_parameters=tool_parameters,
                    retries=retries,
                    correlation_id=correlation_id,
                    oauth_credentials=oauth_credentials,
                    user_id=user_id,
                    mcp_id=mcp_id,
                    node_label=node_label,
                    transition_id=transition_id,
                )
            else:
                available_types = [cfg.get("type") for cfg in config_array]
                raise MCPConfigInvalidError(
                    mcp_id,
                    f"No supported config type found. Available types: {available_types}",
                    {
                        "available_types": available_types,
                        "supported_types": ["streamable-http", "stdio", "sse"],
                    },
                )

            self.logger.info(f"✅ Tool execution completed successfully")
            return result

        except (
            MCPConfigNotFoundError,
            MCPConfigInvalidError,
            MCPAuthenticationError,
            MCPServerUnreachableError,
            MCPToolExecutionError,
            ContainerCreationError,
            ContainerExecutionError,
            SSHConnectionError,
            MCPExecutorError,
        ):
            # Re-raise our custom exceptions - they're already properly categorized
            raise
        except ValueError as ve:
            # Convert ValueError to proper MCP exception
            self.logger.error(f"❌ Configuration validation error: {ve}")
            raise MCPConfigInvalidError(
                mcp_id,
                f"Configuration validation failed: {str(ve)}",
                {"error_type": "validation_error"},
            ) from ve
        except Exception as e:
            self.logger.error(
                f"❌ Tool execution failed with unexpected error: {e}", exc_info=True
            )
            # Convert unexpected errors to categorized exceptions
            raise MCPExecutorError(
                f"Unexpected error during tool execution: {str(e)}",
                error_category=ErrorCategory.SYSTEM_ERROR,
                error_code=ErrorCode.INTERNAL_ERROR,
                details={
                    "mcp_id": mcp_id,
                    "user_id": user_id,
                    "tool_name": tool_name,
                    "error_type": type(e).__name__,
                },
                retryable=False,
            ) from e
