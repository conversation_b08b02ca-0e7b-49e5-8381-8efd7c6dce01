"""
Custom SSH MCP Client for handling problematic containers.

This client bypasses the standard MCP STDIO client and directly implements
the MCP protocol over SSH connections for containers that have issues with
the standard SDK implementation.
"""

import asyncio
import json
import logging
import subprocess
from typing import Any, Dict, List, Optional, Tuple
import time


class CustomSSHMCPClient:
    """Custom SSH-based MCP client for problematic containers."""

    def __init__(
        self,
        ssh_command: List[str],
        container_name: str,
        container_command: str,
        logger: Optional[logging.Logger] = None,
    ):
        self.ssh_command = ssh_command
        self.container_name = container_name
        self.container_command = container_command
        self.logger = logger or logging.getLogger(__name__)

        # Connection state
        self._process: Optional[subprocess.Popen] = None
        self._request_id = 1
        self._initialized = False

    async def connect(self) -> Tuple[Any, Any]:
        """
        Connect to the MCP server and return streams.

        Returns:
            Tuple of (read_stream, write_stream) compatible with MCP session
        """
        self.logger.info("Establishing custom SSH MCP connection...")

        # Create subprocess for SSH connection
        self._process = subprocess.Popen(
            self.ssh_command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=0,  # Unbuffered for real-time communication
        )

        # Wait a moment for the connection to establish
        await asyncio.sleep(1)

        # Check if process is still running
        if self._process.poll() is not None:
            stderr_output = self._process.stderr.read() if self._process.stderr else ""

            # Check for specific SSH errors
            if "Host key verification failed" in stderr_output:
                raise ConnectionError(
                    f"SSH Host key verification failed. This may be resolved by setting up host key verification. Error: {stderr_output}"
                )
            elif "Permission denied" in stderr_output:
                raise ConnectionError(
                    f"SSH authentication failed. Check SSH key and server configuration. Error: {stderr_output}"
                )
            elif "Connection refused" in stderr_output:
                raise ConnectionError(
                    f"SSH connection refused. Check if SSH server is running and accessible. Error: {stderr_output}"
                )
            else:
                raise ConnectionError(
                    f"SSH process terminated immediately. Error: {stderr_output}"
                )

        # Test the connection with a simple MCP initialize message
        await self._test_connection()

        # Create stream-like objects
        read_stream = CustomSSHReadStream(self._process, self.logger)
        write_stream = CustomSSHWriteStream(self._process, self.logger)

        self.logger.info("Custom SSH MCP connection established successfully")
        return (read_stream, write_stream)

    async def _test_connection(self) -> None:
        """Test the connection by sending an initialize message."""
        init_message = {
            "jsonrpc": "2.0",
            "id": self._request_id,
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": "custom-ssh-mcp-client", "version": "1.0.0"},
            },
        }

        # Send initialize message
        message_json = json.dumps(init_message) + "\n"
        self.logger.debug(f"Sending test message: {message_json.strip()}")

        try:
            self._process.stdin.write(message_json)
            self._process.stdin.flush()

            # Wait for response
            response = await self._read_response()

            if response and "result" in response:
                self.logger.info("MCP connection test successful")
                self._initialized = True
                self._request_id += 1
            else:
                raise ConnectionError(f"Invalid response to initialize: {response}")

        except Exception as e:
            self.logger.error(f"Connection test failed: {e}")
            raise ConnectionError(f"Failed to initialize MCP connection: {e}")

    async def _read_response(
        self
    ) -> Optional[Dict[str, Any]]:
        """Read a response from the process."""
        while True:
            if self._process.poll() is not None:
                # Process terminated
                stderr_output = (
                    self._process.stderr.read() if self._process.stderr else ""
                )
                raise ConnectionError(
                    f"Process terminated unexpectedly. Error: {stderr_output}"
                )

            # Try to read a line
            try:
                # Use select or polling to check if data is available
                import select
                import sys

                if sys.platform != "win32":
                    # Unix-like systems
                    ready, _, _ = select.select([self._process.stdout], [], [], 0.1)
                    if ready:
                        line = self._process.stdout.readline()
                        if line:
                            try:
                                return json.loads(line.strip())
                            except json.JSONDecodeError:
                                self.logger.warning(
                                    f"Invalid JSON response: {line.strip()}"
                                )
                                continue
                else:
                    # Windows - use a different approach
                    await asyncio.sleep(0.1)
                    # Try to read without blocking
                    line = self._process.stdout.readline()
                    if line:
                        try:
                            return json.loads(line.strip())
                        except json.JSONDecodeError:
                            self.logger.warning(
                                f"Invalid JSON response: {line.strip()}"
                            )
                            continue

            except Exception as e:
                self.logger.warning(f"Error reading response: {e}")
                await asyncio.sleep(0.1)
                continue

            await asyncio.sleep(0.1)

    async def close(self) -> None:
        """Close the SSH connection."""
        if self._process:
            try:
                self._process.terminate()
                # Wait for process to terminate
                await asyncio.sleep(1)
                if self._process.poll() is None:
                    self._process.kill()
                self.logger.info("Custom SSH MCP connection closed")
            except Exception as e:
                self.logger.warning(f"Error closing SSH connection: {e}")
            finally:
                self._process = None


class CustomSSHReadStream:
    """Custom read stream for SSH MCP connection."""

    def __init__(self, process: subprocess.Popen, logger: logging.Logger):
        self.process = process
        self.logger = logger

    async def read(self, n: int = -1) -> str:
        """Read from the SSH process stdout."""
        try:
            if self.process.poll() is not None:
                raise ConnectionError("SSH process has terminated")

            # Read a line (MCP messages are line-delimited)
            line = self.process.stdout.readline()
            if line:
                return line
            else:
                raise ConnectionError("No data available")

        except Exception as e:
            self.logger.error(f"Error reading from SSH stream: {e}")
            raise


class CustomSSHWriteStream:
    """Custom write stream for SSH MCP connection."""

    def __init__(self, process: subprocess.Popen, logger: logging.Logger):
        self.process = process
        self.logger = logger

    async def write(self, data: str) -> None:
        """Write to the SSH process stdin."""
        try:
            if self.process.poll() is not None:
                raise ConnectionError("SSH process has terminated")

            self.process.stdin.write(data)
            self.process.stdin.flush()

        except Exception as e:
            self.logger.error(f"Error writing to SSH stream: {e}")
            raise

    async def flush(self) -> None:
        """Flush the write stream."""
        try:
            if self.process and self.process.stdin:
                self.process.stdin.flush()
        except Exception as e:
            self.logger.warning(f"Error flushing SSH stream: {e}")
