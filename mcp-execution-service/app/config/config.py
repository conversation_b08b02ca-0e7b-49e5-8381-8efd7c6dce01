import logging
import threading
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    kafka_bootstrap_servers: str = Field(
        default="localhost:9092", alias="KAFKA_BOOTSTRAP_SERVERS"
    )
    kafka_consumer_topic: str = Field(
        default="mcp-execution-request", alias="KAFKA_CONSUMER_TOPIC"
    )
    kafka_consumer_group_id: str = Field(
        default="mcp_executor_service", alias="KAFKA_CONSUMER_GROUP_ID"
    )
    kafka_results_topic: str = Field(default="mcp_results", alias="KAFKA_RESULTS_TOPIC")
    default_mcp_retries: int = Field(default=3, alias="DEFAULT_MCP_RETRIES")
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")
    max_concurrent_tasks: int = Field(default=10, alias="MAX_CONCURRENT_TASKS")

    api_base_url: str = Field(default="http://localhost:8000", alias="API_BASE_URL")

    # Authentication and credential retrieval settings
    server_auth_key: str = Field(default="", alias="SERVER_AUTH_KEY")
    credential_cache_ttl: int = Field(
        default=300, alias="CREDENTIAL_CACHE_TTL"  # 5 minutes
    )

    # SSH Configuration for MCP Client
    ssh_host: str = Field(default="", alias="DEFAULT_SSH_HOST")
    ssh_user: str = Field(default="", alias="DEFAULT_SSH_USER")
    ssh_port: int = Field(default=22, alias="SSH_PORT")
    ssh_key_path: str = Field(default="", alias="SSH_KEY_PATH")
    ssh_key_content: str = Field(default="", alias="DEFAULT_SSH_KEY_CONTENT")

    # MCP Configuration API Settings
    mcp_config_cache_ttl: int = Field(
        default=300, alias="MCP_CONFIG_CACHE_TTL"  # 5 minutes
    )

    # MCP Client Timeout Settings
    mcp_default_timeout: int = Field(default=900, alias="MCP_DEFAULT_TIMEOUT")  # 15 minutes for all operations
    mcp_long_running_timeout: int = Field(default=900, alias="MCP_LONG_RUNNING_TIMEOUT")  # 15 minutes for video/image generation
    mcp_connection_timeout: int = Field(default=30, alias="MCP_CONNECTION_TIMEOUT")  # Connection establishment timeout
    mcp_read_timeout: int = Field(default=900, alias="MCP_READ_TIMEOUT")  # 15 minutes read timeout for streaming responses

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        populate_by_name=True,
        extra="ignore",
    )


class SettingsManager:
    """
    Manages settings with the ability to refresh from environment variables.
    Thread-safe singleton pattern to ensure consistent settings across the app.
    """

    _instance: Optional["SettingsManager"] = None
    _lock = threading.Lock()

    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    cls._instance._settings = None
                    cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        if not self._initialized:
            self._load_settings()
            self._initialized = True

    def _load_settings(self):
        """Load or reload settings from environment variables and .env file."""
        try:
            self._settings = Settings()
        except Exception as e:
            print(f"FATAL: Error loading MCP Executor Service configuration: {e}")
            raise

    def refresh(self):
        """
        Refresh settings by reloading from environment variables and .env file.
        This allows picking up changes to environment variables at runtime.
        """
        with self._lock:
            self._load_settings()
            # Re-configure logging if log level changed
            self._configure_logging()

    def _configure_logging(self):
        """Configure logging based on current settings."""
        log_level_int = getattr(logging, self._settings.log_level.upper(), logging.INFO)

        # Get root logger and update its level
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level_int)

        # Update handlers if they exist
        for handler in root_logger.handlers:
            handler.setLevel(log_level_int)

    @property
    def settings(self) -> Settings:
        """Get current settings instance."""
        return self._settings

    # Proxy properties for easy access to settings attributes
    @property
    def kafka_bootstrap_servers(self) -> str:
        return self._settings.kafka_bootstrap_servers

    @property
    def kafka_consumer_topic(self) -> str:
        return self._settings.kafka_consumer_topic

    @property
    def kafka_consumer_group_id(self) -> str:
        return self._settings.kafka_consumer_group_id

    @property
    def kafka_results_topic(self) -> str:
        return self._settings.kafka_results_topic

    @property
    def default_mcp_retries(self) -> int:
        return self._settings.default_mcp_retries

    @property
    def log_level(self) -> str:
        return self._settings.log_level

    @property
    def max_concurrent_tasks(self) -> int:
        return self._settings.max_concurrent_tasks

    @property
    def ssh_host(self) -> str:
        return self._settings.ssh_host

    @property
    def ssh_user(self) -> str:
        return self._settings.ssh_user

    @property
    def ssh_port(self) -> int:
        return self._settings.ssh_port

    @property
    def ssh_key_path(self) -> str:
        return self._settings.ssh_key_path

    @property
    def ssh_key_content(self) -> str:
        return self._settings.ssh_key_content

    @property
    def api_base_url(self) -> str:
        return self._settings.api_base_url

    @property
    def server_auth_key(self) -> str:
        return self._settings.server_auth_key

    @property
    def credential_cache_ttl(self) -> int:
        return self._settings.credential_cache_ttl

    @property
    def api_base_url(self) -> str:
        return self._settings.api_base_url

    @property
    def mcp_config_cache_ttl(self) -> int:
        return self._settings.mcp_config_cache_ttl


# Create global settings manager instance
_settings_manager = SettingsManager()


# Create a settings proxy that provides the same interface as before
# but allows for refreshing
class SettingsProxy:
    """
    Proxy object that provides the same interface as the old settings object
    but delegates to the refreshable settings manager.
    """

    def __getattr__(self, name):
        return getattr(_settings_manager, name)

    def model_dump(self):
        """Provide model_dump method for compatibility."""
        return _settings_manager.settings.model_dump()

    def refresh(self):
        """Refresh settings from environment variables."""
        _settings_manager.refresh()


# Create the settings object that can be imported and used like before
settings = SettingsProxy()

# Configure initial logging
log_level_int = getattr(logging, settings.log_level.upper(), logging.INFO)

logging.basicConfig(
    level=log_level_int,
    format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s] %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
config_logger = logging.getLogger(__name__)
config_logger.info("MCP Executor Service configuration loaded successfully.")
config_logger.debug(f"MCP Executor Service loaded settings: {settings.model_dump()}")


def refresh_settings():
    """
    Convenience function to refresh settings from environment variables.
    This can be called from anywhere in the application to pick up
    new environment variable values.

    Example:
        from app.config.config import refresh_settings
        refresh_settings()  # Now settings will have latest env values
    """
    settings.refresh()
    config_logger.info("Settings refreshed from environment variables")
    config_logger.debug(f"Refreshed settings: {settings.model_dump()}")
