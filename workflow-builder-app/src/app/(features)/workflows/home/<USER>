"use client";

import React, { useState, useEffect, use<PERSON><PERSON>back, useMemo } from "react";
import { useRouter } from "next/navigation";
import { useTheme } from "next-themes";
import { getClientAccessToken } from "@/lib/clientCookies";
import {
  fetchWorkflowsByUser,
  createEmptyWorkflow,
  WorkflowSummary,
  WorkflowDetails,
  duplicateWorkflow,
} from "../api";
import { useUserStore } from "@/store/userStore";
import {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { <PERSON>roll<PERSON>rea } from "@/components/ui/scroll-area";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Workflow,
  Plus,
  Search,
  AlertCircle,
  Loader2,
  ChevronDown,
  FileEdit,
  File,
  FileUp,
  Moon,
  Sun,
} from "lucide-react";
import { UserProfileButton } from "@/components/auth/UserProfileButton";
import WorkflowCard from "../components/WorkflowCard";
import Image from "next/image";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

export default function WorkflowsListPage() {
  const router = useRouter();
  const user = useUserStore((state) => state.user);
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [workflows, setWorkflows] = useState<WorkflowDetails[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sortOption, setSortOption] = useState<string>("updated_desc");
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [isCreatingWorkflow, setIsCreatingWorkflow] = useState(false);
  const [isDuplicatingId, setIsDuplicatingId] = useState<string | null>(null);
  const [duplicateDialogOpen, setDuplicateDialogOpen] = useState(false);
  const [duplicateWorkflowId, setDuplicateWorkflowId] = useState<string | null>(null);
  const [duplicateFormData, setDuplicateFormData] = useState({
    name: "",
    description: ""
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(9);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  // Theme toggle handler
  const handleToggleTheme = useCallback(() => {
    const newTheme = theme === "dark" ? "light" : "dark";
    setTheme(newTheme);
  }, [theme, setTheme]);

  // Determine if dark mode is active
  const isDarkMode = theme === "dark";

  // Check authentication immediately on mount
  useEffect(() => {
    const checkAuth = () => {
      const cookieToken = getClientAccessToken();
      const tokenFromStore = user?.accessToken;

      if (!cookieToken && !tokenFromStore) {
        console.error("No access token available - redirecting to login");
        setIsRedirecting(true);
        // Use direct window.location for more reliable redirect
        window.location.href = "/login";
      }
    };

    checkAuth();
  }, [user]);

  // Fetch workflows only if not redirecting
  useEffect(() => {
    if (isRedirecting) return;

    const fetchWorkflows = async () => {
      const cookieToken = getClientAccessToken();
      const tokenFromStore = user?.accessToken;
      const accessToken = tokenFromStore || cookieToken;

      // Double-check token availability
      if (!accessToken) {
        console.error("No access token available");
        setIsRedirecting(true);
        window.location.href = "/login";
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const response = await fetchWorkflowsByUser(currentPage, pageSize, accessToken);

        setWorkflows(response?.data);

        // Update pagination state
        if (response.metadata) {
          setTotalPages(response.metadata.totalPages || 1);
          setCurrentPage(response.metadata.currentPage || 1);
        }
      } catch (err) {
        console.error("Failed to fetch workflows:", err);

        // Redirect to login for authentication errors
        if (err instanceof Error && (err.message.includes("401") || err.message.includes("403"))) {
          console.error("Authentication failed:", err);
          setIsRedirecting(true);
          window.location.href = "/login";
          return;
        }

        // Handle other errors
        if (err instanceof Error) {
          if (err.message.includes("404")) {
            setError("No workflows found for this user.");
          } else if (err.message.includes("500")) {
            setError("Server error. Please try again later.");
          } else {
            setError(`Failed to load workflows: ${err.message}`);
          }
        } else {
          setError("Failed to load workflows. Please try again later.");
        }
      } finally {
        setIsLoading(false);
      }
    };

    fetchWorkflows();
  }, [user, currentPage, pageSize, isRedirecting]);

  // Define all hooks unconditionally

  // Helper function to get workflow data regardless of structure
  const getWorkflowData = (data: any): WorkflowDetails => {
    // If it's a WorkflowSummary (has workflow property), return the nested workflow
    if ("workflow" in data && data.workflow) {
      return data.workflow as WorkflowDetails;
    }
    // Otherwise, it's already a WorkflowDetails
    return data as WorkflowDetails;
  };

  // Sort workflows based on selected option
  const sortedWorkflows = useMemo(() => {
    if (!workflows) return [];

    // Filter by search term if provided
    let filtered = workflows;
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = workflows.filter((wf) => {
        const workflowData = getWorkflowData(wf);
        return (
          workflowData.name?.toLowerCase().includes(term) ||
          (workflowData.description && workflowData.description.toLowerCase().includes(term))
        );
      });
    }

    // Sort based on selected option
    return [...filtered].sort((a, b) => {
      const aData = getWorkflowData(a);
      const bData = getWorkflowData(b);

      switch (sortOption) {
        case "updated_desc":
          return (
            new Date(bData.updated_at || "").getTime() - new Date(aData.updated_at || "").getTime()
          );
        case "updated_asc":
          return (
            new Date(aData.updated_at || "").getTime() - new Date(bData.updated_at || "").getTime()
          );
        case "name_asc":
          return (aData.name || "").localeCompare(bData.name || "");
        case "name_desc":
          return (bData.name || "").localeCompare(aData.name || "");
        default:
          return (
            new Date(bData.updated_at || "").getTime() - new Date(aData.updated_at || "").getTime()
          );
      }
    });
  }, [workflows, sortOption, searchTerm]);

  // Handle creating a new workflow
  const handleCreateWorkflow = async () => {
    try {
      setIsCreatingWorkflow(true);
      const newWorkflow = await createEmptyWorkflow();
      console.log("Created new workflow:", newWorkflow);
      // Redirect to canvas page with the new workflow ID
      router.push(`/workflows/${newWorkflow.workflow_id}/edit`);
    } catch (err) {
      console.error("Failed to create workflow:", err);
      setError("Failed to create a new workflow. Please try again.");
      setIsCreatingWorkflow(false);
    }
  };

  // Handle creating workflow from template (placeholder)
  const handleCreateFromTemplate = () => {
    // TODO: Implement template selection functionality
    console.log("Create from template clicked - functionality to be implemented");
  };

  // Handle importing workflow file (placeholder)
  const handleImportFile = () => {
    // TODO: Implement file import functionality
    console.log("Import file clicked - functionality to be implemented");
  };

  // Handler for opening duplicate dialog
  const handleOpenDuplicateDialog = (workflowId: string) => {
    const original = workflows.find(wf => {
      if ("workflow" in wf && wf.workflow && typeof wf.workflow === "object" && "id" in wf.workflow) {
        return (wf.workflow as WorkflowDetails).id === workflowId;
      } else if ("id" in wf) {
        return (wf as WorkflowDetails).id === workflowId;
      }
      return false;
    });

    let originalName = "";
    if (original) {
      if ("workflow" in original && original.workflow && typeof original.workflow === "object" && "name" in original.workflow) {
        originalName = (original.workflow as WorkflowDetails).name || "";
      } else if ("name" in original) {
        originalName = (original as WorkflowDetails).name || "";
      }
    }

    setDuplicateWorkflowId(workflowId);
    setDuplicateFormData({
      name: `${originalName} (Copy)`,
      description: "Duplicated workflow"
    });
    setDuplicateDialogOpen(true);
  };

  // Handler for duplicating a workflow with custom data
  const handleDuplicateWorkflow = async () => {
    if (!duplicateWorkflowId) return;

    setIsDuplicatingId(duplicateWorkflowId);
    try {
      const result = await duplicateWorkflow(duplicateWorkflowId, {
        name: duplicateFormData.name,
        description: duplicateFormData.description
      });

      toast.success("Workflow duplicated successfully!");
      setDuplicateDialogOpen(false);
      setDuplicateWorkflowId(null);
      setDuplicateFormData({ name: "", description: "" });
      // Refresh the workflows list by triggering a re-fetch
      window.location.reload();
    } catch (error) {
      console.error("Failed to duplicate workflow:", error);
      toast.error("Failed to duplicate workflow. Please try again.");
    } finally {
      setIsDuplicatingId(null);
    }
  };

  // Handler for canceling duplicate dialog
  const handleCancelDuplicate = () => {
    setDuplicateDialogOpen(false);
    setDuplicateWorkflowId(null);
    setDuplicateFormData({ name: "", description: "" });
  };

  // Memoize the handleSelectWorkflow function to prevent unnecessary re-renders
  const handleSelectWorkflow = useCallback(
    (workflow: WorkflowSummary | WorkflowDetails) => {
      // Check if the workflow is a WorkflowSummary (has workflow property) or a direct WorkflowDetails
      const workflowId =
        "workflow" in workflow && workflow.workflow
          ? workflow.workflow.id
          : (workflow as WorkflowDetails).id;

      if (workflowId) {
        router.push(`/workflows/${workflowId}/edit`);
      }
    },
    [router],
  );

  return (
    <main className="bg-background min-h-screen">
      {/* Header */}
      <div className="bg-card/30 p-6 shadow-md">
        <div
          className="container"
          style={{
            maxWidth: "100%",
          }}
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Image
                  src={mounted ? (!isDarkMode ? "/wflogo.svg" : "/wflogo_white.svg") : "/wflogo.svg"}
                  alt="Workflow Builder Logo"
                  width={120}
                  height={30}
                  className="h-8 w-auto"
                />
              </div>
            </div>
            <div className="flex items-center gap-3">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    disabled={isCreatingWorkflow}
                    className="bg-brand-primary hover:bg-brand-primary/90 text-white"
                  >
                    {isCreatingWorkflow ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Creating...
                      </>
                    ) : (
                      <>
                        <Plus className="mr-2 h-4 w-4" />
                        Create New Workflow
                        <ChevronDown className="ml-2 h-4 w-4" />
                      </>
                    )}
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuItem
                    onClick={handleCreateWorkflow}
                    disabled={isCreatingWorkflow}
                    className="flex items-center gap-2 py-2"
                  >
                    <FileEdit className="h-4 w-4" />
                    <span>Create from blank</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleCreateFromTemplate}
                    disabled={isCreatingWorkflow}
                    className="flex items-center gap-2 py-2"
                  >
                    <File className="h-4 w-4" />
                    <span>Create from template</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleImportFile}
                    disabled={isCreatingWorkflow}
                    className="flex items-center gap-2 py-2"
                  >
                    <FileUp className="h-4 w-4" />
                    <span>Import file</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              {/* Theme toggle button */}
              {mounted && (
                <button
                  type="button"
                  className="hover:bg-accent hover:text-accent-foreground inline-flex h-8 w-8 items-center justify-center rounded-md text-sm font-medium"
                  onClick={handleToggleTheme}
                  title="Toggle theme"
                >
                  {!isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
                </button>
              )}

              {/* User profile button */}
              <UserProfileButton />
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div
        className="bg-brand-background container px-4 sm:px-8 lg:px-[100px] py-6 sm:py-8"
        style={{
          maxWidth: "100%",
        }}
      >
        {/* Filters and search - responsive */}
        <div className="mb-8 flex flex-col items-center justify-between gap-4 sm:flex-row sm:gap-6">
          <div className="relative w-full max-w-md mx-auto sm:mx-0 sm:max-w-xs md:max-w-md lg:w-80">
            <Search
              className="absolute top-1/2 left-3 h-4 w-4 text-muted-foreground transform -translate-y-1/2"
              aria-hidden="true"
            />
            <Input
              placeholder="Search workflows..."
              className="pl-10 bg-background border-input rounded-lg text-sm font-[Satoshi] w-full"
              style={{
                height: '48px',
                paddingTop: '0',
                paddingBottom: '0',
                lineHeight: '48px',
                display: 'flex',
                alignItems: 'center'
              }}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              aria-label="Search workflows"
              id="workflow-search"
              name="workflow-search"
            />
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <span className="text-sm text-muted-foreground font-[Satoshi] whitespace-nowrap">Sort by:</span>
            <Select value={sortOption} onValueChange={setSortOption}>
              <SelectTrigger className="w-[120px] sm:w-[140px] h-10 bg-background border-input rounded-lg font-[Satoshi]">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent className="bg-background border-input font-[Satoshi]">
                <SelectItem value="updated_desc">Latest update</SelectItem>
                <SelectItem value="updated_asc">Oldest update</SelectItem>
                <SelectItem value="name_asc">Name (A-Z)</SelectItem>
                <SelectItem value="name_desc">Name (Z-A)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Workflows grid */}
        {isLoading ? (
          <div className="flex h-64 items-center justify-center">
            <Loader2 className="text-brand-primary h-8 w-8 animate-spin" />
            <span className="font-[Satoshi] ml-2 text-lg">Loading workflows...</span>
          </div>
        ) : error ? (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <AlertCircle className="text-brand-unpublish mb-4 h-12 w-12" />
            <h3 className="font-[Satoshi] mb-2 text-xl font-semibold">Failed to Load Workflows</h3>
            <p className="text-brand-secondary-font mb-4 font-[Satoshi]">{error}</p>
            <div className="flex gap-4">
              {/* Only show Try Again button for non-authentication errors */}
              {!error.includes("authenticated") && !error.includes("Authentication") && (
                <Button
                  onClick={() => window.location.reload()}
                  className="bg-brand-primary hover:bg-brand-primary/90 text-white"
                >
                  Try Again
                </Button>
              )}
              {/* Always show Go to Login button for authentication errors */}
              {(error.includes("authenticated") || error.includes("Authentication")) && (
                <Button
                  variant="outline"
                  onClick={() => (window.location.href = "/login")}
                  className="border-brand-border-color text-brand-primary hover:bg-brand-clicked"
                >
                  Go to Login
                </Button>
              )}
            </div>
          </div>
        ) : sortedWorkflows.length === 0 ? (
          <div className="flex h-64 flex-col items-center justify-center text-center">
            <Workflow className="text-brand-secondary mb-4 h-12 w-12" />
            <h3 className="font-[Satoshi] mb-2 text-xl font-semibold">No Workflows Found</h3>
            <p className="text-brand-secondary-font mb-4 font-[Satoshi]">
              {searchTerm
                ? "No workflows match your search criteria."
                : "You don't have any workflows yet. Create your first workflow to get started."}
            </p>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  disabled={isCreatingWorkflow}
                  className="bg-brand-primary hover:bg-brand-primary/90 text-white"
                >
                  {isCreatingWorkflow ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      Create New Workflow
                      <ChevronDown className="ml-2 h-4 w-4" />
                    </>
                  )}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-56">
                <DropdownMenuItem
                  onClick={handleCreateWorkflow}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileEdit className="h-4 w-4" />
                  <span>Create from blank</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleCreateFromTemplate}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <File className="h-4 w-4" />
                  <span>Create from template</span>
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={handleImportFile}
                  disabled={isCreatingWorkflow}
                  className="flex items-center gap-2 py-2"
                >
                  <FileUp className="h-4 w-4" />
                  <span>Import file</span>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1 gap-4">
            {sortedWorkflows.map((workflow) => {
              // Generate a key that works for both data structures
              const workflowData = getWorkflowData(workflow);
              const key = workflowData.id || Math.random().toString();

              return (
                <WorkflowCard
                  key={key}
                  workflow={workflow}
                  onClick={handleSelectWorkflow}
                  onDuplicate={handleOpenDuplicateDialog}
                />
              );
            })}

            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination className="mt-6" aria-label="Workflow pagination">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      size="default"
                      onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
                      className={`${currentPage <= 1 ? "pointer-events-none opacity-50" : ""} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}
                      aria-disabled={currentPage <= 1}
                    />
                  </PaginationItem>

                  {/* First page */}
                  {currentPage > 2 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        1
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis if needed */}
                  {currentPage > 3 && (
                    <PaginationItem>
                      <PaginationEllipsis className="text-brand-secondary-font" />
                    </PaginationItem>
                  )}

                  {/* Previous page if not first */}
                  {currentPage > 1 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(currentPage - 1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {currentPage - 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Current page */}
                  <PaginationItem>
                    <PaginationLink
                      size="default"
                      isActive
                      className="brand-gradient-indicator text-brand-white-text border-none"
                    >
                      {currentPage}
                    </PaginationLink>
                  </PaginationItem>

                  {/* Next page if not last */}
                  {currentPage < totalPages && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(currentPage + 1)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {currentPage + 1}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  {/* Ellipsis if needed */}
                  {currentPage < totalPages - 2 && (
                    <PaginationItem>
                      <PaginationEllipsis className="text-brand-secondary-font" />
                    </PaginationItem>
                  )}

                  {/* Last page */}
                  {currentPage < totalPages - 1 && (
                    <PaginationItem>
                      <PaginationLink
                        size="default"
                        onClick={() => setCurrentPage(totalPages)}
                        className="text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke"
                      >
                        {totalPages}
                      </PaginationLink>
                    </PaginationItem>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      size="default"
                      onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
                      className={`${currentPage >= totalPages ? "pointer-events-none opacity-50" : ""} text-brand-primary hover:bg-brand-card-hover hover:text-brand-primary border-brand-stroke`}
                      aria-disabled={currentPage >= totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </div>
        )}
      </div>

      {/* Duplicate Workflow Dialog */}
      <Dialog open={duplicateDialogOpen} onOpenChange={setDuplicateDialogOpen}>
        <DialogContent className="font-[Satoshi]">
          <DialogHeader>
            <DialogTitle className="font-[Satoshi]">Duplicate Workflow</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="duplicate-name" className="text-right font-[Satoshi]">
                Name
              </Label>
              <Input
                id="duplicate-name"
                value={duplicateFormData.name}
                onChange={(e) => setDuplicateFormData({ ...duplicateFormData, name: e.target.value })}
                className="col-span-3 font-[Satoshi]"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="duplicate-description" className="text-right font-[Satoshi]">
                Description
              </Label>
              <Textarea
                id="duplicate-description"
                value={duplicateFormData.description}
                onChange={(e) => setDuplicateFormData({ ...duplicateFormData, description: e.target.value })}
                className="col-span-3 font-[Satoshi]"
              />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={handleCancelDuplicate} className="font-[Satoshi]">
              Cancel
            </Button>
            <Button onClick={handleDuplicateWorkflow} className="font-[Satoshi]">Duplicate</Button>
          </div>
        </DialogContent>
      </Dialog>
    </main>
  );
}

