import React, { useRef, useEffect, useState } from "react";
import { AlertCircle, CheckCircle, Info, XCircle, Loader2, ShieldCheck, ChevronDown, ChevronUp, Clock } from "lucide-react";
import { useTheme } from "next-themes";
import { useExecutionStore } from "@/store/executionStore";
import { dispatchApprovalNeededEvent } from "@/lib/approvalUtils";

// Custom CheckCircle component using the SVG from public folder
const CheckCircleIcon = ({ className }: { className?: string }) => (
  <img
    src="/logo/checkCircle.svg"
    alt="Check Circle"
    className={className}
    width={24}
    height={24}
  />
);

interface LogDisplayProps {
  logs: (string | object)[];
  showStreamingStatus?: boolean;
  showRawResults?: boolean;
}

export function LogDisplay({ logs = [], showStreamingStatus = true, showRawResults = false }: LogDisplayProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const { isStreaming, isExecuting } = useExecutionStore();
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Helper functions for tab content
  const getPrettifyLogs = (logs: (string | object)[]) => {
    // Filter logs to show only those with node_label and meaningful raw results
    return logs.filter((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;
        // Only include logs with node_label and actual raw results
        if (logData.node_label !== undefined && (logData.raw_result || logData.raw_results)) {
          return true;
        }
        // Include error logs that have meaningful error information
        if (logData.status === 'failed' && logData.node_label && (logData.error || logData.result)) {
          return true;
        }
        // Include cancelled/stopped workflow status logs
        if (logData.status === 'cancelled' || logData.workflow_status === 'cancelled' ||
            logData.status === 'stopped' || logData.workflow_status === 'stopped') {
          return true;
        }
      }
      return false;
    }).map((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;

        // Keep node_label, status, and raw result fields for prettify view
        const prettifiedLog: any = {
          node_label: logData.node_label || (logData.status === 'cancelled' || logData.workflow_status === 'cancelled' ? 'Workflow Status' : undefined),
          // Preserve status for proper node status display
          status: logData.status,
          // Preserve workflow_status for approval detection
          workflow_status: logData.workflow_status,
          // Preserve decision for approval detection
          decision: logData.decision,
          // Preserve transition_id for grouping
          transition_id: logData.transition_id,
          // Preserve correlation ID for reference
          correlation_id: logData.correlation_id
        };

        // Include raw result fields
        if (logData.raw_result) {
          prettifiedLog.raw_result = logData.raw_result;
        }
        if (logData.raw_results) {
          prettifiedLog.raw_results = logData.raw_results;
        }

        // Include error information for failed logs
        if (logData.status === 'failed') {
          prettifiedLog.error = logData.error || logData.result;
          prettifiedLog.error_type = logData.error_type;
          // If no raw_result, use the error as the result to display
          if (!prettifiedLog.raw_result && !prettifiedLog.raw_results) {
            prettifiedLog.raw_result = logData.error || logData.result;
          }
        }

        // Include cancellation information for cancelled logs
        if (logData.status === 'cancelled' || logData.workflow_status === 'cancelled') {
          prettifiedLog.cancellation_reason = logData.result || logData.decision || 'Workflow execution was stopped';
          // If no raw_result, use the cancellation reason as the result to display
          if (!prettifiedLog.raw_result && !prettifiedLog.raw_results) {
            prettifiedLog.raw_result = prettifiedLog.cancellation_reason;
          }
        }

        return prettifiedLog;
      }
      return log;
    });
  };

  const getRawLogs = (logs: (string | object)[]) => {
    // Return all logs without any filtering
    return logs;
  };

  // Group prettified logs by node_label
  const groupLogsByNodeLabel = (logs: (string | object)[]) => {
    const grouped: Record<string, (string | object)[]> = {};

    logs.forEach((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;
        const nodeLabel = logData.node_label || 'Unknown Node';
        if (!grouped[nodeLabel]) {
          grouped[nodeLabel] = [];
        }
        grouped[nodeLabel].push(log);
      }
    });

    return grouped;
  };

  // Group all logs by node_label (including status-only logs for status determination)
  const groupAllLogsByNodeLabel = (logs: (string | object)[]) => {
    const grouped: Record<string, (string | object)[]> = {};

    logs.forEach((log) => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;

        // For logs with node_label, use it directly
        let nodeLabel = logData.node_label;

        // For approval logs without node_label, try to find the associated node_label from transition_id
        if (!nodeLabel && logData.transition_id) {
          // Look for a previous log with the same transition_id that has a node_label
          const relatedLog = logs.find(l => {
            if (typeof l === 'object' && l !== null) {
              const relatedLogData = l as any;
              return relatedLogData.transition_id === logData.transition_id && relatedLogData.node_label;
            }
            return false;
          });
          if (relatedLog) {
            nodeLabel = (relatedLog as any).node_label;
          }
        }

        // Include logs that have a node_label OR are approval-related
        if (nodeLabel ||
            logData.status === 'approved' || logData.decision === 'approved' ||
            logData.status === 'paused' || logData.workflow_status === 'waiting_for_approval' ||
            logData.status === 'time_logged' || logData.status === 'complete') {

          const finalNodeLabel = nodeLabel || 'Workflow Status';
          if (!grouped[finalNodeLabel]) {
            grouped[finalNodeLabel] = [];
          }
          grouped[finalNodeLabel].push(log);
        }
      }
    });

    return grouped;
  };

  // Get status icon and loader for a node based on its logs
  const getNodeStatus = (logs: (string | object)[]) => {
    console.log('getNodeStatus called with logs:', logs.length, 'logs');

    // Check global workflow status first
    const workflowStatus = getWorkflowStatus();

    // If workflow is cancelled, all nodes should show stopped status
    if (workflowStatus.status === 'cancelled') {
      return {
        icon: <AlertCircle className="w-6 h-6 text-yellow-400" strokeWidth={1.5} />,
        status: 'Stopped',
        isLoading: false,
        className: isDarkMode ? 'text-yellow-300 bg-zinc-800 border-zinc-600' : 'text-yellow-600 bg-gray-100 border-gray-300'
      };
    }

    // Check if workflow is currently executing and this node doesn't have a final status
    const hasCompletedStatus = logs.some(log => {
      if (typeof log === 'object' && log !== null) {
        const logData = log as any;
        const status = logData.status?.toLowerCase();
        const workflowStatus = logData.status?.toLowerCase();
        return status === 'completed' || status === 'success' || status === 'failed' || status === 'error' ||
               status === 'cancelled' || status === 'stopped' ||
               workflowStatus === 'cancelled' || workflowStatus === 'stopped';
      }
      // Also check for stop execution log messages
      if (typeof log === 'string') {
        const logString = log.toLowerCase();
        return logString.includes('workflow execution manually stopped') ||
               logString.includes('workflow cancelled') ||
               logString.includes('execution was cancelled');
      }
      return false;
    });

    // Get the latest log for this node to check its current status
    const currentLog = logs[logs.length - 1];
    if (typeof currentLog === 'object' && currentLog !== null) {
      const logData = currentLog as any;
      const status = logData.status?.toLowerCase();

      // Check individual node status first - if node has completed, show completed status regardless of workflow state
      switch (status) {
        case 'initialized':
          return {
            icon: <Clock className={`w-6 h-6 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />,
            status: 'Initialized',
            isLoading: false,
            className: isDarkMode ? 'text-blue-300 bg-zinc-800 border-zinc-600' : 'text-blue-600 bg-gray-100 border-gray-300'
          };
        case 'started':
          return {
            icon: <Loader2 className={`w-6 h-6 animate-spin ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />,
            status: 'Started',
            isLoading: true,
            className: isDarkMode ? 'text-blue-300 bg-zinc-800 border-zinc-600' : 'text-blue-600 bg-gray-100 border-gray-300'
          };
        case 'connecting':
          return {
            icon: <Loader2 className={`w-6 h-6 animate-spin ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />,
            status: 'Connecting',
            isLoading: true,
            className: isDarkMode ? 'text-yellow-300 bg-zinc-800 border-zinc-600' : 'text-yellow-600 bg-gray-100 border-gray-300'
          };
        case 'connected':
          return {
            icon: <CheckCircle className={`w-6 h-6 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />,
            status: 'Connected',
            isLoading: false,
            className: isDarkMode ? 'text-green-300 bg-zinc-800 border-zinc-600' : 'text-green-600 bg-gray-100 border-gray-300'
          };
        case 'completed':
        case 'success':
          return {
            icon: <CheckCircleIcon className="w-6 h-6" />,
            status: 'Completed',
            isLoading: false,
            className: isDarkMode ? 'text-green-300 bg-zinc-800 border-zinc-600' : 'text-green-600 bg-gray-100 border-gray-300'
          };
        case 'failed':
        case 'error':
          return {
            icon: <XCircle className="w-6 h-6 text-red-400" strokeWidth={1.5} />,
            status: 'Failed',
            isLoading: false,
            className: isDarkMode ? 'text-red-300 bg-zinc-800 border-zinc-600' : 'text-red-600 bg-gray-100 border-gray-300'
          };
        case 'cancelled':
        case 'stopped':
          return {
            icon: <AlertCircle className="w-6 h-6 text-yellow-400" strokeWidth={1.5} />,
            status: 'Stopped',
            isLoading: false,
            className: isDarkMode ? 'text-yellow-300 bg-zinc-800 border-zinc-600' : 'text-yellow-600 bg-gray-100 border-gray-300'
          };
        case 'time_logged':
          // If we have time_logged, the node completed successfully
          return {
            icon: <CheckCircleIcon className="w-6 h-6" />,
            status: 'Completed',
            isLoading: false,
            className: isDarkMode ? 'text-green-300 bg-zinc-800 border-zinc-600' : 'text-green-600 bg-gray-100 border-gray-300'
          };
        case 'waiting':
        case 'pending_approval':
        case 'paused':
          return {
            icon: <Loader2 className={`w-6 h-6 animate-spin ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />,
            status: 'Waiting for Approval',
            isLoading: true,
            className: isDarkMode ? 'text-yellow-300 bg-zinc-800 border-zinc-600' : 'text-yellow-600 bg-gray-100 border-gray-300'
          };
      }
    }

    // If workflow is executing and node hasn't completed, show loader
    if (isExecuting && !hasCompletedStatus) {
      // Check if this node is currently waiting for approval (not just has approval logs)
      const hasApprovalLog = logs.some(log => {
        if (typeof log === 'string') {
          return log.includes('waiting for approval') || log.includes('Workflow is waiting for approval');
        }
        return false;
      });

      // Check if approval was given (look for approval completion indicators)
      const hasApprovalCompleted = logs.some(log => {
        if (typeof log === 'string') {
          return log.includes('approved') || log.includes('continuing') || log.includes('resumed') || log.includes('Approval sent');
        }
        if (typeof log === 'object' && log !== null) {
          const logData = log as any;
          // Check for approval status in the log object
          if (logData.status === 'approved' || logData.decision === 'approved') {
            console.log('Found approval completion log:', {
              status: logData.status,
              decision: logData.decision,
              workflow_status: logData.workflow_status,
              result: typeof logData.result === 'string' ? logData.result.substring(0, 50) + '...' : logData.result
            });
            return true;
          }
          // Check for completion status
          if (logData.status === 'complete' || logData.workflow_status === 'completed') {
            console.log('Found completion log:', {
              status: logData.status,
              workflow_status: logData.workflow_status
            });
            return true;
          }
          // Check message content
          if (logData.message && (
            logData.message.includes('approved') ||
            logData.message.includes('continuing') ||
            logData.message.includes('resumed') ||
            logData.message.includes('completed')
          )) {
            return true;
          }
          // Check result content
          if (logData.result && typeof logData.result === 'string' && (
            logData.result.includes('Approval granted') ||
            logData.result.includes('executed successfully')
          )) {
            console.log('Found approval result log:', {
              result: logData.result.substring(0, 50) + '...'
            });
            return true;
          }
        }
        return false;
      });

      // Debug logging for approval detection
      console.log('Approval detection results:', {
        hasApprovalLog,
        hasApprovalCompleted,
        totalLogs: logs.length,
        logStatuses: logs.map(log => typeof log === 'object' ? (log as any).status : 'string')
      });

      // Only show waiting for approval if we have approval logs but no completion
      if (hasApprovalLog && !hasApprovalCompleted) {
        console.log('Showing "Waiting for Approval" status');
        return {
          icon: <Loader2 className="w-6 h-6 animate-spin text-white" strokeWidth={1.5} />,
          status: 'Waiting for Approval',
          isLoading: true,
          className: 'text-yellow-300 bg-zinc-800 border-zinc-600'
        };
      }

      return {
        icon: <Loader2 className={`w-6 h-6 animate-spin ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />,
        status: 'Running',
        isLoading: true,
        className: isDarkMode ? 'text-blue-300 bg-zinc-800 border-zinc-600' : 'text-blue-600 bg-gray-100 border-gray-300'
      };
    }



    return {
      icon: <Info className={`h-3 w-3 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={2} />,
      status: 'Unknown',
      isLoading: false,
      className: isDarkMode ? 'text-gray-300 bg-zinc-800 border-zinc-600' : 'text-gray-600 bg-gray-100 border-gray-300'
    };
  };

  // Ensure logs is always an array of strings or objects, parsing JSON strings
  const processedLogs = (Array.isArray(logs) ? logs : []).map((log) => {
    if (log === null || log === undefined) {
      return "";
    }
    if (typeof log === "object") {
      return log; // Keep objects as objects
    }
    if (typeof log === "string") {
      try {
        const parsed = JSON.parse(log);
        if (typeof parsed === 'object' && parsed !== null) {
          return parsed; // Return parsed object if it's valid JSON
        }
      } catch (e) {
        // Not a valid JSON string, return original string
      }
    }
    return String(log); // Coerce any other types to string
  });

  // Determine workflow status from logs
  const getWorkflowStatus = (): {
    status: "in_progress" | "completed" | "failed" | "cancelled" | "waiting_for_approval";
    message: string;
    nodeId?: string;
    nodeName?: string;
  } => {
    // Default status
    let result = {
      status: "in_progress" as const,
      message: "Execution in progress...",
    };

    // Check logs for workflow status, from newest to oldest
    for (let i = processedLogs.length - 1; i >= 0; i--) {
      const log = processedLogs[i];
      let logData: any;

      // If log is already an object, use it directly
      if (typeof log === 'object' && log !== null) {
        logData = log;
      } else {
        // Try to parse the log as JSON if it's a string
        try {
          logData = JSON.parse(String(log));
        } catch (e) {
          // Not a JSON string, might be one of our formatted messages
          // Fallback to string matching for non-JSON logs
          const lowerCaseLog = String(log).toLowerCase();
          if (lowerCaseLog.includes("workflow failed")) {
            return { status: "failed", message: "Execution failed" };
          } else if (lowerCaseLog.includes("workflow completed")) {
            return { status: "completed", message: "Execution completed successfully" };
          } else if (lowerCaseLog.includes("workflow cancelled")) {
            return { status: "cancelled", message: "Execution was cancelled" };
          }
          continue; // Move to the next log if it's not JSON
        }
      }

      // If it's a JSON log, check the workflow_status
      if (logData && logData.workflow_status) {
        const status = logData.workflow_status.toLowerCase();
        switch (status) {
          case "failed":
            return { status: "failed", message: "Execution failed" };
          case "completed":
            return { status: "completed", message: "Execution completed successfully" };
          case "cancelled":
            return { status: "cancelled", message: "Execution was cancelled" };
          case "waiting_for_approval":
            // For approval, we need more specific checks
            if (logData.approval_required === true && logData.status === "paused") {
              const nodeId = logData.node_id || logData.transition_id;
              const nodeName = logData.node_name || logData.node_label || nodeId || "Unknown Node";
              return {
                status: "waiting_for_approval",
                message: "Waiting for approval",
                nodeId: nodeId || undefined,
                nodeName: nodeName,
              };
            }
            break;
          default:
            // For other statuses like 'running', we continue checking previous logs
            break;
        }
      }
    }

    return result;
  };

  // Get current workflow status
  const workflowStatus = getWorkflowStatus();
  const { correlationId } = useExecutionStore();

  // Auto-scroll to bottom when logs update
  // useEffect(() => {
  //   if (scrollRef.current) {
  //     scrollRef.current.scrollIntoView({ behavior: "smooth" });
  //   }
  // }, [processedLogs]);

  // Dispatch approval needed event when status changes to waiting_for_approval
  useEffect(() => {
    if (workflowStatus.status === "waiting_for_approval" && correlationId && workflowStatus.nodeId) {
      console.log("LogDisplay: Detected valid waiting_for_approval status, dispatching event");

      // Only dispatch if we have a valid node ID (not undefined or "unknown")
      if (workflowStatus.nodeId && workflowStatus.nodeId !== "unknown") {
        // Use the centralized function to dispatch the event
        dispatchApprovalNeededEvent(
          correlationId,
          workflowStatus.nodeId,
          workflowStatus.nodeName || workflowStatus.nodeId
        );

        // Also set the window flag directly for immediate access
        window._pendingApproval = {
          correlationId,
          nodeId: workflowStatus.nodeId,
          nodeName: workflowStatus.nodeName || workflowStatus.nodeId,
          timestamp: Date.now()
        };

        // Force a UI update
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('approval-ui-update'));
        }, 200);

        // Log the approval request for debugging
        console.log("Dispatched approval event for:", {
          correlationId,
          nodeId: workflowStatus.nodeId,
          nodeName: workflowStatus.nodeName || workflowStatus.nodeId
        });
      } else {
        console.log("Skipping approval event dispatch due to missing node ID");
      }
    }
  }, [workflowStatus.status, correlationId, workflowStatus.nodeId, workflowStatus.nodeName]);

  // Parse log line to determine type and format
  const parseLogLine = (logEntry: string | object) => {
    // Default values
    let type = "info";
    let icon = <Info className="h-4 w-4 flex-shrink-0 text-blue-400" />;
    let className = isDarkMode ? "text-blue-300 bg-zinc-800 border-zinc-600" : "text-blue-600 bg-gray-100 border-gray-300";

    // For structured JSON logs, check the status field first
    if (typeof logEntry === 'object' && logEntry !== null) {
      const logData = logEntry as any;
      if (logData.status) {
        const status = logData.status.toLowerCase();
        switch (status) {
          case 'completed':
          case 'success':
            type = "success";
            icon = <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-400" />;
            className = isDarkMode ? "text-green-300 bg-zinc-800 border-zinc-600" : "text-green-600 bg-gray-100 border-gray-300";
            return { type, icon, className };
          case 'failed':
          case 'error':
            type = "error";
            icon = <XCircle className="h-4 w-4 flex-shrink-0 text-red-400" />;
            className = isDarkMode ? "text-red-300 bg-zinc-800 border-zinc-600" : "text-red-600 bg-gray-100 border-gray-300";
            return { type, icon, className };
          case 'connecting':
          case 'connected':
          case 'started':
          case 'time_logged':
            type = "info";
            icon = <Info className="h-4 w-4 flex-shrink-0 text-blue-400" />;
            className = isDarkMode ? "text-blue-300 bg-zinc-800 border-zinc-600" : "text-blue-600 bg-gray-100 border-gray-300";
            return { type, icon, className };
        }
      }
    }

    // Fall back to text-based analysis for unstructured logs
    const logString = typeof logEntry === 'string' ? logEntry : JSON.stringify(logEntry);

    // Check for error messages using word boundaries for more precise matching
    if (
      /\b(error|exception|failed)\b/i.test(logString) ||
      logString.toLowerCase().includes("❌")
    ) {
      type = "error";
      icon = <XCircle className="h-4 w-4 flex-shrink-0 text-red-400" />;
      className = isDarkMode ? "text-red-300 bg-zinc-800 border-zinc-600" : "text-red-600 bg-gray-100 border-gray-300";
    }
    // Check for success messages
    else if (
      /\b(success|completed|finished)\b/i.test(logString) ||
      logString.toLowerCase().includes("✅")
    ) {
      type = "success";
      icon = <CheckCircle className="h-4 w-4 flex-shrink-0 text-green-400" />;
      className = isDarkMode ? "text-green-300 bg-zinc-800 border-zinc-600" : "text-green-600 bg-gray-100 border-gray-300";
    }
    // Check for warning messages
    else if (/\b(warning|warn)\b/i.test(logString)) {
      type = "warning";
      icon = <AlertCircle className="h-4 w-4 flex-shrink-0 text-yellow-400" />;
      className = isDarkMode ? "text-yellow-300 bg-zinc-800 border-zinc-600" : "text-yellow-600 bg-gray-100 border-gray-300";
    }

    return { type, icon, className };
  };

  // LogEntry component to handle individual log line rendering (simplified for prettify view)
  const LogEntry = ({ log, index, isPrettifyView = false }: { log: string | object; index: number; isPrettifyView?: boolean }) => {
    // For prettify view with raw results, start expanded to show raw results
    const [isExpanded, setIsExpanded] = useState(isPrettifyView);
    const { icon, className } = parseLogLine(log);

    let logData: any = null;
    let isCollapsible = false;

    if (typeof log === 'object' && log !== null) {
      logData = log;
    } else {
      try {
        logData = JSON.parse(String(log));
      } catch (e) {
        // Not a JSON log
        logData = null;
      }
    }

    // For prettify view, we don't need collapsible behavior - just show raw results directly
    if (isPrettifyView && logData && typeof logData === 'object') {
      // In prettify view, we're showing raw results so no need for collapsible behavior
      isCollapsible = false;
    }
    // For raw view, determine if log should be collapsible
    else if (!isPrettifyView && logData && typeof logData === 'object') {
      const hasWorkflowId = typeof logData.workflowId === 'string' || typeof logData.workflow_id === 'string';
      const hasTransactionId = typeof logData.transactionId === 'string' || typeof logData.transaction_id === 'string' || typeof logData.transition_id === 'string';
      const hasMessage = typeof logData.message === 'string';

      // Count how many of the key fields we have
      const fieldCount = [hasWorkflowId, hasTransactionId, hasMessage].filter(Boolean).length;

      // Only make collapsible if we have at least 2 of the key fields
      if (fieldCount >= 2) {
        isCollapsible = true;
      }
    }

    const toggleExpand = () => {
      setIsExpanded(!isExpanded);
    };

    return (
      <div
        key={index}
        className={`flex flex-col border p-2 ${className}`}
      >
        {/* Special rendering for prettify view with raw results */}
        {isPrettifyView ? (
          // // <div className="flex flex-col">
          //   {/* <div className="flex items-start gap-2">
          //     {icon}
          //     <div className="flex-1">
          //       <span className="font-medium">Raw Result:</span>
          //     </div>
          //   </div> */}
            <div className={`pl-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
              {/* Special handling for error logs */}
              {logData && logData.status === 'failed' ? (
                <div className="space-y-2">
                  <div className="text-red-400 font-medium text-sm">Error Details:</div>
                  <div className={`${isDarkMode ? 'bg-red-900/20 border-red-500/30' : 'bg-red-50 border-red-200'} border rounded p-3`}>
                    <pre className={`whitespace-pre-wrap text-xs ${isDarkMode ? 'text-red-300' : 'text-red-700'}`}>
                      {logData.error || logData.raw_result || 'Unknown error occurred'}
                    </pre>
                    {logData.error_type && (
                      <div className={`mt-2 text-xs ${isDarkMode ? 'text-red-400' : 'text-red-600'}`}>
                        Error Type: {logData.error_type}
                      </div>
                    )}
                  </div>
                </div>
              ) : (
                <pre className={`whitespace-pre-wrap text-xs ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {(() => {
                    // Display raw_result or raw_results
                    if (logData && typeof logData === 'object') {
                      if (logData.raw_result) {
                        return JSON.stringify(logData.raw_result, null, 2);
                      } else if (logData.raw_results) {
                        return JSON.stringify(logData.raw_results, null, 2);
                      }
                    }
                    return JSON.stringify(logData, null, 2);
                  })()}
                </pre>
              )}
              {logData && logData.correlation_id && (
                <div className={`mt-2 text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                  Correlation ID: {logData.correlation_id}
                </div>
              )}
            </div>
          // </div>
        ) : (
          // Original rendering for raw view
          <>
            <div className="flex items-start gap-2 relative">
              {icon}
              {isCollapsible ? (
                <div className="flex-1">
                  <span className="break-all whitespace-pre-wrap pr-8">
                    {(() => {
                      const displayWorkflowId = logData.workflowId || logData.workflow_id;
                      const displayTransactionId = logData.transactionId || logData.transaction_id || logData.transition_id;
                      const displayMessage = logData.message;

                      const elements = [];

                      if (displayWorkflowId) {
                        elements.push(
                          <span key="workflowId">
                            <strong>Workflow ID:</strong> {displayWorkflowId}
                          </span>
                        );
                      }

                      if (displayTransactionId) {
                        elements.push(
                          <span key="transactionId">
                            <strong>Transaction ID:</strong> {displayTransactionId}
                          </span>
                        );
                      }

                      if (displayMessage) {
                        elements.push(
                          <span key="message">
                            <strong>Message:</strong> {displayMessage}
                          </span>
                        );
                      }

                      return elements.map((element, idx) => (
                        <React.Fragment key={element.key}>
                          {idx > 0 && <br />}
                          {element}
                        </React.Fragment>
                      ));
                    })()}
                  </span>
                  <button
                    onClick={toggleExpand}
                    className={`p-1.5 rounded-md ${isDarkMode ? 'hover:bg-zinc-700' : 'hover:bg-gray-200'} focus:outline-none focus:ring-2 focus:ring-purple-500 inline-flex items-center justify-center absolute top-0 right-0`}
                    aria-expanded={isExpanded}
                    aria-controls={`log-details-${index}`}
                  >
                    {isExpanded ? (
                      <ChevronUp className={`h-4 w-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`} />
                    ) : (
                      <ChevronDown className={`h-4 w-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`} />
                    )}
                  </button>
                </div>
              ) : (
                <span className={`break-all whitespace-pre-wrap ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                  {typeof log === 'object' && log !== null ? JSON.stringify(log, null, 2) : String(log)}
                </span>
              )}
            </div>
            {isCollapsible && isExpanded && (
              <div id={`log-details-${index}`} className={`mt-2 pl-6 ${isDarkMode ? 'text-gray-300 bg-zinc-900' : 'text-gray-700 bg-gray-100'} p-2 rounded`}>
                <pre className="whitespace-pre-wrap text-xs">{JSON.stringify(logData, null, 2)}</pre>
              </div>
            )}
          </>
        )}
      </div>
    );
  };

  // Filter out duplicate "Workflow is waiting for approval" messages
  const filteredLogs = processedLogs.reduce((acc: (string | object)[], log: string | object, index: number) => {
    // Skip duplicate "Workflow is waiting for approval" messages
    if (
      typeof log === 'string' && log.startsWith("⏸️ Workflow is waiting for approval") &&
      index > 0 &&
      typeof processedLogs[index - 1] === 'string' && processedLogs[index - 1].startsWith("⏸️ Workflow is waiting for approval")
    ) {
      return acc;
    }

    // Add the log to the filtered list
    acc.push(log);
    return acc;
  }, []);

  // Process logs for different views
  const prettifyLogs = getPrettifyLogs(filteredLogs);
  const rawLogs = getRawLogs(filteredLogs);
  const groupedLogs = groupLogsByNodeLabel(prettifyLogs);

  // Debug logging for prettify logs
  console.log('LogDisplay - Prettify logs:', JSON.stringify(prettifyLogs.map(log => ({
    node_label: (log as any).node_label,
    status: (log as any).status,
    workflow_status: (log as any).workflow_status,
    decision: (log as any).decision,
    transition_id: (log as any).transition_id,
    result: typeof (log as any).result === 'string' ? (log as any).result.substring(0, 100) + '...' : (log as any).result
  })), null, 2));

  console.log('LogDisplay - Grouped logs:', JSON.stringify(Object.keys(groupedLogs).map(nodeLabel => ({
    nodeLabel,
    logCount: groupedLogs[nodeLabel].length,
    statuses: groupedLogs[nodeLabel].map(log => (log as any).status),
    workflow_statuses: groupedLogs[nodeLabel].map(log => (log as any).workflow_status),
    decisions: groupedLogs[nodeLabel].map(log => (log as any).decision)
  })), null, 2));

  // Store in window for debugging
  (window as any).lastPrettifyLogs = prettifyLogs;
  (window as any).lastGroupedLogs = groupedLogs;
  // FIX: Use all logs (not filtered) for status grouping
  const allGroupedLogs = groupAllLogsByNodeLabel(processedLogs);

  // Component for rendering node label dropdown with status
  const NodeLabelDropdown = ({ nodeLabel, logs, allLogsForStatus }: {
    nodeLabel: string;
    logs: (string | object)[];
    allLogsForStatus: (string | object)[]
  }) => {
    const [isExpanded, setIsExpanded] = useState(false);
    console.log(`NodeLabelDropdown for ${nodeLabel} - allLogsForStatus:`, JSON.stringify(allLogsForStatus.map(log => ({
      status: (log as any).status,
      workflow_status: (log as any).workflow_status,
      decision: (log as any).decision,
      node_label: (log as any).node_label,
      transition_id: (log as any).transition_id
    })), null, 2));
    const nodeStatus = getNodeStatus(allLogsForStatus);

    return (
      <div className=" w-full">
        <div
          className={`flex items-center justify-between p-5 border cursor-pointer transition-colors ${isDarkMode ? 'bg-[#2E2E2E] border-[#4B4B4D] hover:bg-[#3A3A3A]' : 'bg-gray-100 border-gray-300 hover:bg-gray-200'} w-full`}
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex items-center gap-3">
            {nodeStatus.isLoading ? (
              <Loader2 className={`w-6 h-6 animate-spin ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />
            ) : (
              nodeStatus.icon
            )}
            <div className="flex flex-row items-center text-center gap-3">
              <span className={`font-bold text-base leading-[140%] tracking-[-0.02em] ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                {nodeLabel}
              </span>
              <span className={`font-normal text-xs leading-4 ${isDarkMode ? 'text-[#6F6F6F]' : 'text-gray-600'}`}>
                Status: {nodeStatus.status}
              </span>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {isExpanded ? (
              <ChevronUp className={`h-5 w-5 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />
            ) : (
              <ChevronDown className={`h-5 w-5 ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />
            )}
          </div>
        </div>

        {isExpanded && (
          <div className={`space-y-1 ${isDarkMode ? 'bg-[#2E2E2E] border-[#4B4B4D]' : 'bg-gray-50 border-gray-300'} p-4 border`}>
            {logs.map((log: string | object, index: number) => (
              <LogEntry key={`${nodeLabel}-${index}`} log={log} index={index} isPrettifyView={true} />
            ))}
          </div>
        )}
      </div>
    );
  };

  const renderPrettifyContent = () => {
    const nodeLabels = Object.keys(groupedLogs);

    if (nodeLabels.length === 0) {
      return (
        <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} py-8 text-center`}>
          <Info className="mx-auto mb-2 h-8 w-8 opacity-50" />
          <p>No logs with node labels available. Run the workflow to see execution logs.</p>
        </div>
      );
    }

    return (
      <>
        {nodeLabels.map((nodeLabel) => (
          <NodeLabelDropdown
            key={nodeLabel}
            nodeLabel={nodeLabel}
            logs={groupedLogs[nodeLabel]}
            allLogsForStatus={allGroupedLogs[nodeLabel] || []}
          />
        ))}

        {/* Streaming status indicator */}
        {showStreamingStatus && isStreaming && (
          <div className={`flex items-center gap-2 rounded border ${isDarkMode ? 'border-zinc-600 bg-zinc-800 text-blue-300' : 'border-gray-300 bg-gray-100 text-blue-600'} p-2`}>
            <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
            <span>Streaming logs in real-time...</span>
          </div>
        )}
      </>
    );
  };

  const renderRawContent = () => {
    return (
      <>
        {rawLogs.map((log: string | object, index: number) => (
          <LogEntry key={index} log={log} index={index} isPrettifyView={false} />
        ))}

        {/* Streaming status indicator */}
        {showStreamingStatus && isStreaming && (
          <div className={`flex items-center gap-2 rounded border ${isDarkMode ? 'border-zinc-600 bg-zinc-800 text-blue-300' : 'border-gray-300 bg-gray-100 text-blue-600'} p-2`}>
            <Loader2 className="h-4 w-4 animate-spin text-blue-400" />
            <span>Streaming logs in real-time...</span>
          </div>
        )}

        {/* Execution status indicator */}
        {showStreamingStatus &&
          !isStreaming &&
          processedLogs.length > 0 &&
          workflowStatus.status !== "in_progress" && (
            <div
              className={`flex items-center gap-2 rounded border p-2 ${
                workflowStatus.status === "failed"
                  ? isDarkMode ? "border-red-600 bg-zinc-800 text-red-300" : "border-red-300 bg-red-50 text-red-700"
                  : workflowStatus.status === "completed"
                    ? isDarkMode ? "border-green-600 bg-zinc-800 text-green-300" : "border-green-300 bg-green-50 text-green-700"
                    : workflowStatus.status === "cancelled"
                      ? isDarkMode ? "border-yellow-600 bg-zinc-800 text-yellow-300" : "border-yellow-300 bg-yellow-50 text-yellow-700"
                      : workflowStatus.status === "waiting_for_approval"
                        ? isDarkMode ? "border-blue-600 bg-zinc-800 text-blue-300" : "border-blue-300 bg-blue-50 text-blue-700"
                        : isDarkMode ? "border-green-600 bg-zinc-800 text-green-300" : "border-green-300 bg-green-50 text-green-700"
              }`}
            >
              {workflowStatus.status === "failed" ? (
                <XCircle className="h-4 w-4 text-red-400" />
              ) : workflowStatus.status === "completed" ? (
                <CheckCircle className="h-4 w-4 text-green-400" />
              ) : workflowStatus.status === "cancelled" ? (
                <AlertCircle className="h-4 w-4 text-yellow-400" />
              ) : workflowStatus.status === "waiting_for_approval" ? (
                <ShieldCheck className="h-4 w-4 text-blue-400" />
              ) : (
                <CheckCircle className="h-4 w-4 text-green-400" />
              )}
              <span>{workflowStatus.message}</span>
            </div>
          )}
      </>
    );
  };

  // Check if we should show loading state for prettify view
  const shouldShowLoader = !showRawResults && isExecuting && prettifyLogs.length === 0 && logs.length > 0;

  return (
    <div className="space-y-1 font-mono text-xs">
      {shouldShowLoader ? (
        <div className="flex flex-col items-center justify-center py-8 space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          <div className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-sm`}>Processing logs...</div>
        </div>
      ) : (
        showRawResults ? renderRawContent() : renderPrettifyContent()
      )}
      <div ref={scrollRef} />
    </div>
  );
}
