import React, { useCallback, useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";
import {
  Download,
  Play,
  AlertCircle,
  CheckCircle,
  FileText,
  Code,
  X,
  Info,
  BadgeCheck,
  HelpCircle,
  Square,
  Loader,
  RotateCcw
} from "lucide-react";
import { useTheme } from "next-themes";
import { LogDisplay } from "./LogDisplay";
import { useExecutionStore } from "@/store/executionStore";
import { executeWorkflowWithUserInputs, WorkflowExecuteWithUserInputsPayload } from "@/lib/api";
import { SSEClient } from "@/lib/sseClient";
import { shouldIncludeField, logFieldStatus } from "@/lib/field-utils";
import { clearApprovalEvent, clearAllApprovalEvents } from "@/lib/approvalUtils";
import { isNodeConnectedAsTool } from "@/utils/toolConnectionUtils";
import { parseInputValue } from "@/utils/valueFormatting";


interface ExecutionDialogProps {
  onClose: () => void;
  onStopExecution?: () => void;
  workflowId?: string;
}

export function ExecutionDialog({ onClose, onStopExecution, workflowId }: ExecutionDialogProps) {
  // Get theme from next-themes
  const { theme } = useTheme();
  const isDarkMode = theme === 'dark';

  // Get state from Zustand store
  const {
    isDialogOpen,
    missingFields,
    // setMissingFields is no longer used since we're not adding fields from StartNode parameters
    activeTab,
    setActiveTab,
    fieldValues,
    setFieldValues,
    updateFieldValue,
    errors,
    setErrors,
    isFormValid,
    setIsFormValid,
    logs,
    addLog,
    isExecuting,
    setIsExecuting,
    setIsStreaming,
    correlationId,
    setCorrelationId,
  } = useExecutionStore();

  // Ref for auto-scrolling logs
  const logsEndRef = useRef<HTMLDivElement>(null);

  // Ref for SSE client
  const sseClientRef = useRef<SSEClient | null>(null);

  // Local state for raw results toggle
  const [showRawResults, setShowRawResults] = useState(false);

  // Validate form - memoized to avoid dependency issues
  const validateForm = useCallback(
    (values: Record<string, any>, errors: Record<string, string>) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(`[${timestamp}] [validateForm] ========== VALIDATING FORM ==========`);
      console.log(
        `[${timestamp}] [validateForm] Total fields to validate: ${missingFields.length}`,
      );
      console.log(
        `[${timestamp}] [validateForm] Total values provided: ${Object.keys(values).length}`,
      );
      console.log(`[${timestamp}] [validateForm] Total errors: ${Object.keys(errors).length}`);

      // Check if all required fields have values and no errors
      let allFieldsValid = true;
      let requiredFieldsCount = 0;
      let validFieldsCount = 0;

      // First pass: count required fields and check if all fields are valid
      missingFields.forEach((field) => {
        const fieldId = `${field.nodeId}_${field.name}`;
        // For boolean fields, consider them as having a value even if false
        const hasValue =
          field.inputType === "boolean" ? values[fieldId] !== undefined : !!values[fieldId];
        const hasError = !!errors[fieldId];
        // Consider fields required unless explicitly marked as optional
        const isRequired = field.required !== false;

        console.log(`[${timestamp}] [validateForm] Checking field: ${fieldId}`);
        console.log(`[${timestamp}] [validateForm] - Field type: ${field.inputType}`);
        console.log(
          `[${timestamp}] [validateForm] - Is required: ${isRequired ? "YES" : "NO"} (required !== false: ${field.required !== false})`,
        );
        console.log(`[${timestamp}] [validateForm] - Has value: ${hasValue ? "YES" : "NO"}`);
        console.log(
          `[${timestamp}] [validateForm] - Current value: ${JSON.stringify(values[fieldId])}`,
        );
        console.log(`[${timestamp}] [validateForm] - Has error: ${hasError ? "YES" : "NO"}`);
        if (hasError) {
          console.log(`[${timestamp}] [validateForm] - Error message: ${errors[fieldId]}`);
        }

        if (isRequired) {
          requiredFieldsCount++;
        }

        // Field is valid if:
        // 1. It's not required, OR
        // 2. It has a value AND no errors
        const isFieldValid = !isRequired || (hasValue && !hasError);
        console.log(
          `[${timestamp}] [validateForm] - Field is valid: ${isFieldValid ? "YES" : "NO"}`,
        );

        if (isFieldValid) {
          validFieldsCount++;
        } else {
          allFieldsValid = false;
        }
      });

      console.log(
        `[${timestamp}] [validateForm] Required fields: ${requiredFieldsCount}, Valid fields: ${validFieldsCount}`,
      );
      console.log(
        `[${timestamp}] [validateForm] Form validation result: ${allFieldsValid ? "VALID" : "INVALID"}`,
      );

      // Force immediate update of isFormValid state
      setIsFormValid(allFieldsValid);

      // Return the validation result so it can be used immediately
      return allFieldsValid;
    },
    [missingFields, setIsFormValid],
  );

  // Ref to track the workflowId for which the dialog was last initialized
  const workflowIdRef = useRef<string | undefined>(undefined);

  // Initialize form values when dialog opens
  useEffect(() => {
    // This effect should only run when the dialog opens (`isDialogOpen` becomes true)
    // or when the workflowId changes while the dialog is open.
    if (!isDialogOpen) {
      // When the dialog closes, we don't want to clear logs.
      // The state is already persisted.
      return;
    }

    const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
    console.log(
      `[${timestamp}] [ExecutionDialog] ========== INITIALIZING EXECUTION DIALOG ==========`,
    );

    const { clearLogs } = useExecutionStore.getState();

    // Check if the workflowId has changed since the last time the dialog was initialized
    // or if it's the very first time the dialog is being opened (workflowIdRef.current is undefined)
    if (workflowIdRef.current !== workflowId) {
      console.log(`[${timestamp}] [ExecutionDialog] Workflow ID changed or first open. Clearing logs.`);
      clearLogs();
      workflowIdRef.current = workflowId; // Update the ref to the current workflowId
    } else {
      console.log(`[${timestamp}] [ExecutionDialog] Same workflow ID (${workflowIdRef.current}). Not clearing logs on dialog open.`);
    }

    const initialValues: Record<string, any> = {};
    const initialErrors: Record<string, string> = {};
    console.log(`[${timestamp}] [ExecutionDialog] Missing fields count: ${missingFields.length}`);
    // Note: We're intentionally not using window.startNodeCollectedParameters to ensure fresh input values

    // Log details about missing fields
    if (missingFields.length > 0) {
      console.log(`[${timestamp}] [ExecutionDialog] Missing fields details:`);
      missingFields.forEach((field, index) => {
        console.log(
          `[${timestamp}] [ExecutionDialog]   ${index + 1}. Node: ${field.nodeName} (${field.nodeId}), Field: ${field.displayName} (${field.name}), Type: ${field.inputType}`,
        );
        console.log("The field", field);
        // Ensure field has proper display_name and node_name
        if (!field.displayName) {
          console.log(
            `[${timestamp}] [ExecutionDialog] WARNING: Field ${index + 1} is missing display_name, using name instead`,
          );
          field.displayName = field.name || "Unnamed Field";
        }

        if (!field.nodeId) {
          console.log(
            `[${timestamp}] [ExecutionDialog] WARNING: Field ${index + 1} is missing node_name, using "Unknown Node" instead`,
          );
          field.nodeName = "Unknown Node";
        }
      });
    }

    // Get the current nodes from the window object
    // This is used to check if a node still exists in the workflow
    const currentNodeIds = new Set(window.currentWorkflowNodes?.map((node: any) => node.id) || []);
    console.log(
      `[${timestamp}] [ExecutionDialog] Current node IDs in workflow:`,
      Array.from(currentNodeIds),
    );

    // For prebuilt workflows, the currentNodeIds might be empty even though the nodes exist
    // In this case, we should trust the missingFields array which was populated by the validation
    const isPrebuiltWorkflow = currentNodeIds.size === 0 && missingFields.length > 0;
    if (isPrebuiltWorkflow) {
      console.log(
        `[${timestamp}] [ExecutionDialog] Detected prebuilt workflow - currentNodeIds is empty but missingFields has ${missingFields.length} fields`,
      );
      console.log(
        `[${timestamp}] [ExecutionDialog] Will not filter out fields based on node existence for prebuilt workflow`,
      );
    }

    // Create a list of all fields that should be displayed
    // This includes both missing fields and fields with values in the StartNode
    // Filter out fields from nodes that no longer exist in the workflow, but only if not a prebuilt workflow
    // Also filter out fields that are not connected to the Start node
    // And filter out fields that already have values configured in the inspector panel
    // CRITICAL FIX: Also filter out fields from tool-connected components (defensive check)
    const allFields = [...missingFields].filter((field) => {
      // DEFENSIVE CHECK: Skip fields from nodes connected as tools to agentic components
      // This is a safety net in case tool parameters slip through the validation system
      const currentEdges = window.currentWorkflowEdges || [];
      if (isNodeConnectedAsTool(field.nodeId, currentEdges)) {
        console.log(
          `[${timestamp}] [ExecutionDialog] DEFENSIVE FILTER: Excluding field from tool-connected node: ${field.nodeName} (${field.nodeId}) - field: ${field.name}`,
        );
        return false;
      }
      // For prebuilt workflows, skip the node existence check
      if (isPrebuiltWorkflow) {
        // For loaded workflows, we need to handle the case where required might not be explicitly set
        // Consider fields required unless explicitly marked as optional (required: false)
        const isRequired = field.required !== false;
        // Also include fields directly connected to the Start node
        const isDirectlyConnected = field.directly_connected_to_start === true;

        // Check if the field already has a value configured in the inspector panel
        // First, find the node in the current workflow
        const node = window.currentWorkflowNodes?.find((node: any) => node.id === field.nodeId);

        // Check if the node has a config with a value for this field
        const hasConfiguredValue = node?.data?.config && node.data.config[field.name] !== undefined;

        // Check if this field has an incoming connection from another node
        const hasIncomingConnection = field.is_handle === true && field.is_connected === true;

        // Use the shared utility function to log field status
        logFieldStatus(
          "ExecutionDialog",
          `${field.nodeId}_${field.name}`,
          field.nodeName,
          field.name,
          isRequired,
          field.required,
          isDirectlyConnected,
          hasConfiguredValue,
          hasConfiguredValue ? node.data.config[field.name] : undefined,
          hasIncomingConnection,
        );

        // Use the shared utility function to determine if the field should be included
        return shouldIncludeField(
          isRequired,
          isDirectlyConnected,
          hasConfiguredValue,
          hasIncomingConnection,
        );
      }

      // For regular workflows, first check if the node still exists
      const nodeExists = currentNodeIds.has(field.nodeId);
      if (!nodeExists) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Filtering out field from deleted node: ${field.nodeName} (${field.nodeId})`,
        );
        return false;
      }

      // Check if the node is connected to the Start node
      if (!field.connected_to_start) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Filtering out field from node not connected to Start node: ${field.nodeName} (${field.nodeId})`,
        );
        return false;
      }

      // For loaded workflows, we need to handle the case where required might not be explicitly set
      // Consider fields required unless explicitly marked as optional (required: false)
      const isRequired = field.required !== false;
      // Also include fields directly connected to the Start node
      const isDirectlyConnected = field.directly_connected_to_start === true;

      // Check if the field already has a value configured in the inspector panel
      // Find the node in the current workflow
      const node = window.currentWorkflowNodes?.find((node: any) => node.id === field.nodeId);

      // Check if the node has a config with a value for this field
      const hasConfiguredValue = node?.data?.config && node.data.config[field.name] !== undefined;

      // Check if this field has an incoming connection from another node
      const hasIncomingConnection = field.is_handle === true && field.is_connected === true;

      // Use the shared utility function to log field status
      logFieldStatus(
        "ExecutionDialog",
        `${field.nodeId}_${field.name}`,
        field.nodeName,
        field.name,
        isRequired,
        field.required,
        isDirectlyConnected,
        hasConfiguredValue,
        hasConfiguredValue ? node.data.config[field.name] : undefined,
        hasIncomingConnection,
      );

      // Use the shared utility function to determine if the field should be included
      return shouldIncludeField(
        isRequired,
        isDirectlyConnected,
        hasConfiguredValue,
        hasIncomingConnection,
      );
    });

    // We're not using StartNode parameters anymore to ensure fresh input values
    // Instead, we'll just use the missing fields that were collected during validation

    // Log the fields we're going to use
    console.log(
      `[${timestamp}] [ExecutionDialog] Using ${allFields.length} fields from validation`,
    );

    // No need to update missing fields since we're not adding any new fields from StartNode parameters

    // Get a snapshot of the current fieldValues to avoid dependency issues
    const currentFieldValues = { ...fieldValues };

    // Process all fields to set initial values
    console.log(
      `[${timestamp}] [ExecutionDialog] ========== PROCESSING FIELDS FOR INITIALIZATION ==========`,
    );
    console.log(`[${timestamp}] [ExecutionDialog] Total fields to process: ${allFields.length}`);
    console.log(
      `[${timestamp}] [ExecutionDialog] Existing field values: ${Object.keys(currentFieldValues).length}`,
    );

    allFields.forEach((field) => {
      const fieldId = `${field.nodeId}_${field.name}`;
      console.log(`[${timestamp}] [ExecutionDialog] Processing field: ${fieldId}`);
      console.log(
        `[${timestamp}] [ExecutionDialog] Field details - Node: ${field.nodeName}, Name: ${field.name}, Type: ${field.inputType}`,
      );

      // Always use fresh values, even if we already have a value for this field in the current session
      if (currentFieldValues[fieldId] !== undefined) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Not using existing value for ${fieldId} to ensure fresh input`,
        );
      }

      // Check if the field has a currentValue from the missing fields collection
      if (field.currentValue !== undefined) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Raw currentValue from field collection for ${fieldId}:`,
          field.currentValue,
        );
        console.log(
          `[${timestamp}] [ExecutionDialog] currentValue type: ${typeof field.currentValue}`,
        );

        // Always use currentValue as-is since it should already be properly unwrapped
        // The unwrapping should have happened in fieldValidation.ts
        console.log(
          `[${timestamp}] [ExecutionDialog] Using currentValue as-is for ${fieldId}:`,
          field.currentValue,
        );
        initialValues[fieldId] = field.currentValue;
      } else {
        console.log(
          `[${timestamp}] [ExecutionDialog] No currentValue found, using default values for field ${fieldId}`,
        );

        // If no value in StartNode, use default values based on input type
        console.log(
          `[${timestamp}] [ExecutionDialog] Setting default value for field ${field.name} (${field.inputType})`,
        );

        // Handle different input types with appropriate defaults
        switch (field.inputType) {
          case "string":
          case "text":
            initialValues[fieldId] = "";
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty string for string field ${fieldId}`,
            );
            break;

          case "number":
          case "int":
          case "float":
            initialValues[fieldId] = 0;
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default 0 for numeric field ${fieldId}`,
            );
            break;

          case "boolean":
          case "bool":
            initialValues[fieldId] = false;
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default false for boolean field ${fieldId}`,
            );
            break;

          case "dropdown":
            // For dropdowns, use the first option or empty string
            if (field.options && field.options.length > 0) {
              initialValues[fieldId] = field.options[0];
              console.log(
                `[${timestamp}] [ExecutionDialog] Set default first option "${field.options[0]}" for dropdown field ${fieldId}`,
              );
            } else {
              initialValues[fieldId] = "";
              console.log(
                `[${timestamp}] [ExecutionDialog] Set default empty string for dropdown field ${fieldId} (no options available)`,
              );
            }
            break;

          case "object":
          case "dict":
          case "json":
            // Try to extract properties from the field info
            try {
              // Special case for keywords field
              if (field.name === "keywords" || field.name === "tool_arg_keywords") {
                const keywordsObj = {
                  time: "",
                  objective: "",
                  audience: "",
                  gender: "",
                  tone: "",
                  speakers: "",
                };
                initialValues[fieldId] = keywordsObj; // Store as native object, not JSON string
                console.log(
                  `[${timestamp}] [ExecutionDialog] Set default keywords object for field ${fieldId}:`,
                  keywordsObj,
                );
              } else {
                // For other object fields, use empty object as native object
                initialValues[fieldId] = {}; // Store as native object, not JSON string
                console.log(
                  `[${timestamp}] [ExecutionDialog] Set default empty object for object field ${fieldId}`,
                );
              }
            } catch (e) {
              console.error(
                `[${timestamp}] [ExecutionDialog] Error initializing object field ${field.name}:`,
                e,
              );
              initialValues[fieldId] = {}; // Store as native object, not JSON string
              console.log(
                `[${timestamp}] [ExecutionDialog] Set default empty object after error for field ${fieldId}`,
              );
            }
            break;

          case "array":
          case "list":
            initialValues[fieldId] = []; // Store as native array, not JSON string
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty array for array field ${fieldId}`,
            );
            break;

          case "credential":
            initialValues[fieldId] = "";
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty string for credential field ${fieldId}`,
            );
            break;

          default:
            initialValues[fieldId] = "";
            console.log(
              `[${timestamp}] [ExecutionDialog] Set default empty string for unknown type field ${fieldId} (type: ${field.inputType})`,
            );
            break;
        }
      }

      console.log(
        `[${timestamp}] [ExecutionDialog] Final value for ${fieldId}: ${JSON.stringify(initialValues[fieldId])}`,
      );

      // Log whether this field will have an initial error
      const fieldValue = initialValues[fieldId];
      const isRequired = field.required !== false;
      let hasInitialValue = false;

      if (field.inputType === "boolean") {
        // For boolean fields, any defined value (true/false) is valid
        hasInitialValue = fieldValue !== undefined && fieldValue !== null;
      } else if (field.inputType === "number") {
        // For number fields, 0 is a valid value, but empty string or undefined is not
        hasInitialValue = fieldValue !== undefined && fieldValue !== null && fieldValue !== "";
      } else if (field.inputType === "array" || field.inputType === "list") {
        // For array fields, empty array [] is valid if it was explicitly set
        hasInitialValue = fieldValue !== undefined && fieldValue !== null && fieldValue !== "";
      } else if (field.inputType === "object" || field.inputType === "dict" || field.inputType === "json") {
        // For object fields, empty object {} is valid if it was explicitly set
        hasInitialValue = fieldValue !== undefined && fieldValue !== null && fieldValue !== "";
      } else {
        // For string and other fields, empty string is not valid for required fields
        hasInitialValue = fieldValue !== undefined && fieldValue !== null && fieldValue !== "";
      }

      if (isRequired && !hasInitialValue) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Field ${fieldId} will have initial error: This field is required`,
        );
        // Set initial error if required field has no value
        initialErrors[fieldId] = "This field is required";
      }
    });

    // Only update field values if we have new values to set
    if (Object.keys(initialValues).length > 0) {
      console.log(`[${timestamp}] [ExecutionDialog] ========== UPDATING FORM STATE ==========`);
      console.log(
        `[${timestamp}] [ExecutionDialog] Setting ${Object.keys(initialValues).length} initial field values`,
      );

      // Log each field being set
      Object.entries(initialValues).forEach(([fieldId, value]) => {
        console.log(
          `[${timestamp}] [ExecutionDialog] Setting field ${fieldId} = ${JSON.stringify(value)}`,
        );
      });

      // Log initial errors
      if (Object.keys(initialErrors).length > 0) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Setting ${Object.keys(initialErrors).length} initial field errors`,
        );
        Object.entries(initialErrors).forEach(([fieldId, errorMsg]) => {
          console.log(`[${timestamp}] [ExecutionDialog] Field error: ${fieldId} - ${errorMsg}`);
        });
      } else {
        console.log(`[${timestamp}] [ExecutionDialog] No initial field errors`);
      }

      // Merge with existing values
      const mergedValues = {
        ...currentFieldValues,
        ...initialValues,
      };

      // Get a snapshot of the current errors to avoid dependency issues
      const currentErrors = { ...errors };

      // Clear errors for fields that have valid values
      const validatedErrors = { ...currentErrors };

      // Check each field value and clear errors for valid values
      Object.entries(mergedValues).forEach(([fieldId, value]) => {
        // Find the field definition
        const fieldDef = missingFields.find((field) => `${field.nodeId}_${field.name}` === fieldId);
        if (fieldDef) {
          const type = fieldDef.inputType || "string";
          const isRequired = fieldDef.required !== false;

          // Use the same validation logic as elsewhere
          let hasValue = false;
          if (type === "boolean") {
            hasValue = value !== undefined && value !== null;
          } else if (type === "number") {
            hasValue = value !== undefined && value !== null && value !== "";
          } else if (type === "array" || type === "list") {
            hasValue = value !== undefined && value !== null && value !== "";
          } else if (type === "object" || type === "dict" || type === "json") {
            hasValue = value !== undefined && value !== null && value !== "";
          } else {
            hasValue = value !== undefined && value !== null && value !== "";
          }

          // If the field has a valid value, clear any "required" errors
          if (hasValue || !isRequired) {
            if (validatedErrors[fieldId] === "This field is required") {
              delete validatedErrors[fieldId];
              console.log(`[${timestamp}] [ExecutionDialog] Cleared required error for field ${fieldId} with value: ${JSON.stringify(value)}`);
            }
          }
        }
      });

      console.log(
        `[${timestamp}] [ExecutionDialog] Total field values after merge: ${Object.keys(mergedValues).length}`,
      );
      console.log(
        `[${timestamp}] [ExecutionDialog] Total field errors after validation: ${Object.keys(validatedErrors).length}`,
      );

      // Update state
      setFieldValues(mergedValues);
      setErrors(validatedErrors);

      // Validate form
      console.log(`[${timestamp}] [ExecutionDialog] Validating form with merged values and validated errors`);
      const isValid = validateForm(mergedValues, validatedErrors);

      // Force immediate update of button state if needed
      if (isValid) {
        console.log(
          `[${timestamp}] [ExecutionDialog] Initial form validation result: VALID, Run button should be enabled`,
        );
        // Dispatch a custom event for immediate UI updates
        window.dispatchEvent(new CustomEvent("form-validation-complete", { detail: { isValid } }));
      } else {
        console.log(
          `[${timestamp}] [ExecutionDialog] Initial form validation result: INVALID, Run button should be disabled`,
        );
      }
    } else {
      console.log(`[${timestamp}] [ExecutionDialog] No new field values to set`);
    }

    // No need for cleanup function here since we handle the reset in the main effect body
  }, [isDialogOpen, missingFields, validateForm]);

  // Auto-scroll logs to bottom
  useEffect(() => {
    if (logsEndRef.current) {
      logsEndRef.current.scrollIntoView({ behavior: "smooth" });
    }
  }, [logs]);

  // Clean up SSE connection when component unmounts
  useEffect(() => {
    return () => {
      // Close any existing SSE connection
      if (sseClientRef.current) {
        console.log("Closing SSE connection on component unmount");
        sseClientRef.current.close();
        sseClientRef.current = null;
      }

      // Reset workflow status
      setWorkflowStatus(null);
    };
  }, []);

  // Track toast interactions to prevent dialog closure
  useEffect(() => {
    let toastInteractionTimeout: NodeJS.Timeout;
    
    const handleToastInteraction = (_event: Event) => {
      // Mark that a toast interaction is happening
      (window as any)._toastInteractionActive = true;
      
      // Clear any existing timeout
      if (toastInteractionTimeout) {
        clearTimeout(toastInteractionTimeout);
      }
      
      // Reset the flag after a short delay
      toastInteractionTimeout = setTimeout(() => {
        (window as any)._toastInteractionActive = false;
      }, 300);
    };

    // Listen for various toast-related events
    const toastEvents = ['click', 'mousedown', 'keydown', 'focus'];
    
    // Add event listeners to detect toast interactions
    toastEvents.forEach(eventType => {
      document.addEventListener(eventType, (event) => {
        const target = event.target as Element;
        if (target && (
          target.closest('[data-sonner-toast]') ||
          target.closest('[data-sonner-toaster]') ||
          target.closest('[data-close-button="true"]') ||
          target.closest('[data-sonner-close-button]') ||
          target.classList.contains('toast-container')
        )) {
          handleToastInteraction(event);
        }
      }, true);
    });

    return () => {
      if (toastInteractionTimeout) {
        clearTimeout(toastInteractionTimeout);
      }
      // Clean up the global flag
      (window as any)._toastInteractionActive = false;
    };
  }, []);

  // State for approval request and workflow status
  const [approvalNeeded, setApprovalNeeded] = useState(false);
  const [workflowStatus, setWorkflowStatus] = useState<string | null>(null);
  const [approvalDetails, setApprovalDetails] = useState<{
    correlationId: string;
    nodeId: string;
    nodeName: string;
    timestamp?: number;
    approvalKey?: string;
  } | null>(null);

  // State for dialog content warnings (currently unused but kept for future use)
  const [, setDialogContentWarnings] = useState<string[]>([]);

  // State for button loading states
  const [isApproveLoading, setIsApproveLoading] = useState(false);
  const [isRegenerateLoading, setIsRegenerateLoading] = useState(false);

  // Function to add default descriptions to dialog content
  const addDefaultDescriptions = useCallback(() => {
    const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
    console.log(
      `[${timestamp}] [addDefaultDescriptions] Adding default descriptions to dialog content`,
    );

    // This is a temporary fix - in the future, these descriptions should be added
    // in the node configuration where the dialog content is defined

    // Clear any existing warnings
    setDialogContentWarnings([]);

    // In a real implementation, you would modify the node configuration
    // For now, we'll just log that this action would be taken
    console.log(
      `[${timestamp}] [addDefaultDescriptions] In a real implementation, this would modify the node configuration to add descriptions`,
    );

    // Add a log entry about fixing the warnings
    addLog("✅ Added default descriptions to dialog content");
  }, [addLog]);

  // Listen for workflow terminal status events
  useEffect(() => {
    const handleTerminalStatus = (event: CustomEvent) => {
      console.log("Received workflow-terminal-status event:", event.detail);

      // Check if we're in approval state - don't reset approval-related states if we are
      const isInApprovalState = workflowStatus === "waiting_for_approval" && approvalNeeded;

      // Reset execution state
      setIsExecuting(false);
      setIsStreaming(false);

      // If we're not in approval state, reset form validation and field values
      if (!isInApprovalState) {
        // Reset form validation state
        // We'll set this to false so that the user has to fill in the form again
        setIsFormValid(false);

        // Reset field values to empty to force user to fill them in again
        const emptyValues: Record<string, any> = {};
        const newErrors: Record<string, string> = {};

        // Initialize empty values for all fields
        missingFields.forEach((field) => {
          const fieldId = `${field.nodeId}_${field.name}`;

          // Set empty values based on field type
          if (field.inputType === "boolean" || field.inputType === "bool") {
            emptyValues[fieldId] = false;
          } else if (
            field.inputType === "number" ||
            field.inputType === "int" ||
            field.inputType === "float"
          ) {
            emptyValues[fieldId] = 0;
          } else if (
            field.inputType === "object" ||
            field.inputType === "dict" ||
            field.inputType === "json"
          ) {
            emptyValues[fieldId] = "{}";
          } else if (field.inputType === "array" || field.inputType === "list") {
            emptyValues[fieldId] = "[]";
          } else {
            emptyValues[fieldId] = "";
          }

          // Set validation errors for required fields
          if (
            field.required !== false &&
            field.inputType !== "boolean" &&
            field.inputType !== "bool"
          ) {
            newErrors[fieldId] = "This field is required";
          }
        });

        // Update field values and errors
        setFieldValues(emptyValues);
        setErrors(newErrors);

        // Only clear approval state if we're not in approval state
        setApprovalNeeded(false);
        setWorkflowStatus(null);

        // Clear from the tracking system if we have details
        if (approvalDetails) {
          clearApprovalEvent(approvalDetails.correlationId, approvalDetails.nodeId);
        }

        setApprovalDetails(null);
      } else {
        // If we are in approval state, make sure the Run Again button is disabled
        setIsFormValid(false);
        console.log("Workflow is in approval state, keeping approval UI visible");
      }
    };

    // Add event listener
    window.addEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);

    // Clean up
    return () => {
      window.removeEventListener("workflow-terminal-status", handleTerminalStatus as EventListener);
    };
  }, [
    setIsFormValid,
    setIsStreaming,
    setIsExecuting,
    setFieldValues,
    setErrors,
    setApprovalNeeded,
    setWorkflowStatus,
    setApprovalDetails,
    missingFields,
    approvalDetails,
    clearApprovalEvent,
  ]);

  // Listen for form validation complete events
  useEffect(() => {
    const handleFormValidationComplete = (event: CustomEvent) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(
        `[${timestamp}] [FormValidationComplete] Received form-validation-complete event:`,
        event.detail,
      );

      if (event.detail && event.detail.isValid) {
        console.log(
          `[${timestamp}] [FormValidationComplete] Form is valid, forcing button state update`,
        );
        // Force update the UI to enable the Run button
        setIsFormValid(true);
      }
    };

    // Add event listener
    window.addEventListener(
      "form-validation-complete",
      handleFormValidationComplete as EventListener,
    );

    // Clean up
    return () => {
      window.removeEventListener(
        "form-validation-complete",
        handleFormValidationComplete as EventListener,
      );
    };
  }, [setIsFormValid]);

  // Listen for workflow approval needed events
  useEffect(() => {
    let lastProcessedTimestamp = 0;

    const handleApprovalNeeded = (event: CustomEvent) => {
      console.log("Received workflow-approval-needed event:", event.detail);

      // Force processing of events that have the approvalKey property (from our improved utility)
      // or if it's a newer event than the last one we processed
      // or if the event has the force flag set
      const shouldProcess =
        event.detail.approvalKey ||
        event.detail.force ||
        !event.detail.timestamp ||
        event.detail.timestamp > lastProcessedTimestamp;

      if (shouldProcess) {
        lastProcessedTimestamp = event.detail.timestamp || Date.now();

        // Create approval key to check if already processed
        const approvalKey = `${event.detail.correlationId}-${event.detail.nodeId}`;

        // Check if we've already processed this approval
        if (processedApprovals.has(approvalKey)) {
          console.log("Approval event already processed, skipping:", approvalKey);
          return;
        }

        // Mark as processed
        setProcessedApprovals(prev => new Set(prev).add(approvalKey));

        // Switch to logs tab to show the approval UI
        setActiveTab("logs");

        // Set approval state with a slight delay to ensure UI updates
        // This helps with race conditions in React's state updates
        setTimeout(() => {
          setApprovalNeeded(true);
          setWorkflowStatus("waiting_for_approval");
          setApprovalDetails(event.detail);

          // Log the approval state for debugging
          console.log("Setting approval state:", {
            approvalNeeded: true,
            workflowStatus: "waiting_for_approval",
            approvalDetails: event.detail,
          });
        }, 100);

        console.log(
          `Processed approval event for node ${event.detail.nodeName} (${event.detail.nodeId}) with timestamp ${lastProcessedTimestamp}`,
        );
      } else {
        console.log(
          `Ignoring older approval event with timestamp ${event.detail.timestamp} (last processed: ${lastProcessedTimestamp})`,
        );
      }
    };

    // Handle direct UI update events
    const handleApprovalUIUpdate = () => {
      console.log("Received approval-ui-update event");

      // Check if we have a pending approval that needs to be displayed
      if (window._pendingApproval) {
        console.log("Processing pending approval from UI update event:", window._pendingApproval);

        // Switch to logs tab
        setActiveTab("logs");

        // Set approval state
        setApprovalNeeded(true);
        setApprovalDetails({
          correlationId: window._pendingApproval.correlationId,
          nodeId: window._pendingApproval.nodeId,
          nodeName: window._pendingApproval.nodeName,
          timestamp: window._pendingApproval.timestamp,
        });
      }
    };

    // Add event listeners
    window.addEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
    window.addEventListener("approval-ui-update", handleApprovalUIUpdate as EventListener);

    // Add a periodic check for pending approvals that might have been missed
    const checkInterval = setInterval(() => {
      if (window._pendingApproval && !approvalNeeded) {
        console.log("Found pending approval that wasn't processed:", window._pendingApproval);

        // Manually trigger the approval UI
        setApprovalNeeded(true);
        setApprovalDetails({
          correlationId: window._pendingApproval.correlationId,
          nodeId: window._pendingApproval.nodeId,
          nodeName: window._pendingApproval.nodeName,
          timestamp: window._pendingApproval.timestamp,
        });

        // Switch to logs tab
        setActiveTab("logs");
      }
    }, 2000);

    // Clean up
    return () => {
      window.removeEventListener("workflow-approval-needed", handleApprovalNeeded as EventListener);
      window.removeEventListener("approval-ui-update", handleApprovalUIUpdate as EventListener);
      clearInterval(checkInterval);
      // Clear all approval events when component unmounts
      clearAllApprovalEvents();
    };
  }, [setActiveTab, approvalNeeded]);

  // Track processed approvals to prevent re-triggering
  const [processedApprovals, setProcessedApprovals] = useState<Set<string>>(new Set());

  // Check logs for approval needed status
  useEffect(() => {
    // Only check if we're not already in approval mode and we have logs
    if (!approvalNeeded && logs.length > 0 && correlationId) {
      // Check the last 10 logs (or all logs if fewer than 10)
      const logsToCheck = logs.slice(Math.max(0, logs.length - 10));

      for (const log of logsToCheck) {
        let logData: any = null;

        // Handle both string and object logs
        if (typeof log === 'object' && log !== null) {
          logData = log;
        } else if (typeof log === 'string') {
          // Only look for string logs that match our specific criteria
          if (
            log.includes("workflow_status") &&
            log.includes("waiting_for_approval") &&
            log.includes("approval_required") &&
            log.includes("status") &&
            log.includes("paused")
          ) {
            console.log("Found potential approval request in logs, checking details");

            try {
              logData = JSON.parse(log);
            } catch (e) {
              console.error("Failed to parse log as JSON:", e);
              continue;
            }
          } else {
            continue; // Skip string logs that don't match criteria
          }
        } else {
          continue; // Skip invalid log types
        }

        // Use node_id if available, otherwise fall back to transition_id
        const nodeId = logData.node_id || logData.transition_id;
        const nodeName = logData.node_name || logData.node_label || nodeId;

        // Only consider it a valid approval request if it meets specific criteria
        if (
          logData &&
          logData.workflow_status === "waiting_for_approval" &&
          logData.approval_required === true &&
          logData.status === "paused" &&
          nodeId
        ) {
              // Create a unique key for this approval request
              const approvalKey = `${correlationId}-${nodeId}`;

              // Check if we've already processed this approval
              if (processedApprovals.has(approvalKey)) {
                console.log("Approval already processed, skipping:", approvalKey);
                continue;
              }

              console.log("Found valid approval request in logs:", {
                correlationId,
                nodeId: nodeId,
                nodeName: nodeName,
                approvalKey,
                usedTransitionId: !logData.node_id && logData.transition_id,
              });

              // Mark this approval as processed to prevent re-triggering
              setProcessedApprovals(prev => new Set(prev).add(approvalKey));

              // Set approval state
              setApprovalNeeded(true);
              setApprovalDetails({
                correlationId,
                nodeId: nodeId,
                nodeName: nodeName,
                timestamp: Date.now(),
              });

              // Reset workflow status to waiting_for_approval (in case it was "regenerating")
              setWorkflowStatus("waiting_for_approval");

              // Switch to logs tab
              setActiveTab("logs");

              // Also set the window flag directly for immediate access
              window._pendingApproval = {
                correlationId,
                nodeId: nodeId,
                nodeName: nodeName,
                timestamp: Date.now(),
              };

              // Add to approval history for debugging
              if (window._approvalEventHistory) {
                window._approvalEventHistory.push({
                  correlationId,
                  nodeId: nodeId,
                  nodeName: nodeName,
                  timestamp: Date.now(),
                  status: "detected_in_logs",
                });
              }

              // Force a UI update
              setTimeout(() => {
                window.dispatchEvent(new CustomEvent("approval-ui-update"));
              }, 200);

              break;
        } else {
          console.log("Log contains waiting_for_approval but doesn't meet criteria:", logData);
        }
      }
    }
  }, [logs, approvalNeeded, correlationId, setActiveTab]);

  // Handle when approval is sent
  const handleApprovalSent = () => {
    console.log("🔄 handleApprovalSent called - starting approval cleanup...");
    console.log("Current approval state:", { approvalNeeded, approvalDetails });

    // Add to approval history for debugging
    if (window._approvalEventHistory && approvalDetails) {
      window._approvalEventHistory.push({
        correlationId: approvalDetails.correlationId,
        nodeId: approvalDetails.nodeId,
        nodeName: approvalDetails.nodeName || approvalDetails.nodeId,
        timestamp: Date.now(),
        status: "approval_sent",
      });
    }

    // Clear approval state completely
    console.log("🧹 Clearing approval state...");
    setApprovalNeeded(false);

    // Reset workflow status to running
    setWorkflowStatus("running");

    // Clear from the tracking system if we have details
    if (approvalDetails) {
      console.log("🗑️ Clearing approval tracking data...");
      clearApprovalEvent(approvalDetails.correlationId, approvalDetails.nodeId);

      // Also clear the window flag
      window._pendingApproval = undefined;
    }

    // Reset approval details
    setApprovalDetails(null);

    // Log the state change
    console.log("✅ Approval state cleared successfully");
  };

  // Handle field change - memoized to prevent unnecessary re-renders
  const handleFieldChange = useCallback(
    (fieldId: string, value: any, type: string) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(`[${timestamp}] [handleFieldChange] ========== FIELD CHANGE DETECTED ==========`);
      console.log(`[${timestamp}] [handleFieldChange] Field: ${fieldId}`);
      console.log(`[${timestamp}] [handleFieldChange] Type: ${type}`);
      console.log(`[${timestamp}] [handleFieldChange] New value: ${JSON.stringify(value)}`);
      console.log(
        `[${timestamp}] [handleFieldChange] Previous value: ${JSON.stringify(fieldValues[fieldId])}`,
      );

      // Extract node ID and field name from the field ID for logging purposes
      const parts = fieldId.split("_");
      const nodeId = parts[0];
      const fieldName = parts.slice(1).join("_");
      console.log(
        `[${timestamp}] [handleFieldChange] Field belongs to node ${nodeId}, field name: ${fieldName}`,
      );

      // Find the field definition to check if it's required
      const fieldDef = missingFields.find((field) => `${field.nodeId}_${field.name}` === fieldId);
      // Consider fields required unless explicitly marked as optional
      const isRequired = fieldDef?.required !== false;
      console.log(
        `[${timestamp}] [handleFieldChange] Field is required: ${isRequired ? "YES" : "NO"} (required !== false: ${fieldDef?.required !== false})`,
      );

      // Validate field
      const newErrors = { ...errors };
      console.log(
        `[${timestamp}] [handleFieldChange] Current errors: ${JSON.stringify(newErrors[fieldId] || "none")}`,
      );

      // Perform validation based on field type
      // Improved validation that properly handles different field types
      let hasValue = false;

      if (type === "boolean") {
        // For boolean fields, any defined value (true/false) is valid
        hasValue = value !== undefined && value !== null;
      } else if (type === "number") {
        // For number fields, 0 is a valid value, but empty string or undefined is not
        hasValue = value !== undefined && value !== null && value !== "";
      } else if (type === "array" || type === "list") {
        // For array fields, empty array [] is valid if it was explicitly set
        hasValue = value !== undefined && value !== null && value !== "";
      } else if (type === "object" || type === "dict" || type === "json") {
        // For object fields, empty object {} is valid if it was explicitly set
        hasValue = value !== undefined && value !== null && value !== "";
      } else {
        // For string and other fields, empty string is not valid for required fields
        hasValue = value !== undefined && value !== null && value !== "";
      }

      console.log(
        `[${timestamp}] [handleFieldChange] Value validation - value: ${JSON.stringify(value)}, type: ${type}, hasValue: ${hasValue}`,
      );

      if (isRequired && !hasValue) {
        console.log(
          `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Field is required but empty (value: ${JSON.stringify(value)})`,
        );
        newErrors[fieldId] = "This field is required";
      } else if (
        type === "object" ||
        type === "dict" ||
        type === "json" ||
        type === "array" ||
        type === "list"
      ) {
        console.log(`[${timestamp}] [handleFieldChange] Validating ${type} field`);

        if (typeof value === "string" && value.trim() !== "") {
          // Validate JSON string format
          try {
            JSON.parse(value);
            console.log(`[${timestamp}] [handleFieldChange] JSON string validation passed`);
            delete newErrors[fieldId];
          } catch (e: any) {
            console.log(
              `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid JSON format - ${e.message}`,
            );
            newErrors[fieldId] = "Invalid JSON format";
          }
        } else if (typeof value === "object" && value !== null) {
          // Native object - validate it's a valid object structure
          try {
            // Test if the object can be serialized (catches circular references, etc.)
            JSON.stringify(value);
            console.log(`[${timestamp}] [handleFieldChange] Native object validation passed`);
            delete newErrors[fieldId];
          } catch (e: any) {
            console.log(
              `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid object structure - ${e.message}`,
            );
            newErrors[fieldId] = "Invalid object structure";
          }
        } else if (
          value === "" ||
          value === null ||
          value === undefined ||
          (Array.isArray(value) && value.length === 0) ||
          (typeof value === "object" && Object.keys(value).length === 0)
        ) {
          // Empty value is valid (will be handled by required field validation)
          console.log(`[${timestamp}] [handleFieldChange] Empty ${type} field is valid`);
          delete newErrors[fieldId];
        } else {
          console.log(
            `[${timestamp}] [handleFieldChange] VALIDATION ERROR: Invalid ${type} value type`,
          );
          newErrors[fieldId] = `Invalid ${type} value`;
        }
      } else {
        console.log(`[${timestamp}] [handleFieldChange] Field validation passed`);
        delete newErrors[fieldId];
      }

      // Update field value in store
      console.log(`[${timestamp}] [handleFieldChange] Updating field value in store`);
      updateFieldValue(fieldId, value);

      // Log validation result
      if (newErrors[fieldId]) {
        console.log(`[${timestamp}] [handleFieldChange] Field has error: ${newErrors[fieldId]}`);
      } else {
        console.log(`[${timestamp}] [handleFieldChange] Field is valid`);
      }

      // Update errors
      console.log(`[${timestamp}] [handleFieldChange] Updating errors in state`);
      setErrors(newErrors);

      // Create merged values for form validation
      const mergedValues = {
        ...fieldValues,
        [fieldId]: value,
      };

      console.log(`[${timestamp}] [handleFieldChange] Validating entire form with updated values`);

      // Validate form - use the return value to update local state immediately
      const isValid = validateForm(mergedValues, newErrors);
      console.log(
        `[${timestamp}] [handleFieldChange] Form validation result: ${isValid ? "VALID" : "INVALID"}`,
      );

      // Force immediate update of button state if needed
      if (isValid) {
        console.log(
          `[${timestamp}] [handleFieldChange] Form is now valid, Run button should be enabled`,
        );
        // We could dispatch a custom event here if needed for immediate UI updates
        window.dispatchEvent(new CustomEvent("form-validation-complete", { detail: { isValid } }));
      }
    },
    [missingFields, errors, fieldValues, updateFieldValue, setErrors, validateForm],
  );

  // Function to set up SSE connection
  const setupSSEConnection = useCallback(
    (correlationId: string) => {
      const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
      console.log(
        `[${timestamp}] [setupSSEConnection] Setting up SSE connection for correlation ID: ${correlationId}`,
      );

      // Close any existing connection
      if (sseClientRef.current) {
        console.log(`[${timestamp}] [setupSSEConnection] Closing existing SSE connection`);
        sseClientRef.current.close();
        sseClientRef.current = null;
      }

      // Create a new SSE client
      const sseClient = new SSEClient(correlationId, {
        onOpen: () => {
          console.log(`[${timestamp}] [setupSSEConnection] SSE connection opened`);
          setIsStreaming(true);
          setWorkflowStatus("running");
          addLog("Connected to execution stream...");
        },
        onMessage: (event) => {
          // This handles any unnamed events from the server
          const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
          console.log(`[${timestamp}] [setupSSEConnection] SSE generic message received:`, event.data);
          // Avoid logging empty keep-alive messages
          if (event.data && event.data !== "{}") {
            addLog(event.data);
          }
        },
        onCustomEvent: (eventType, data) => {
          const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
          console.log(`[${timestamp}] [setupSSEConnection] SSE custom event received [${eventType}]:`, data);

          switch (eventType) {
            case "connection":
              const connectionLogData = typeof data === "object" && data !== null ? JSON.stringify(data, null, 2) : data;
              addLog(`Stream connected: ${connectionLogData}`);
              break;
            case "keep-alive":
              // Don't log keep-alive messages to avoid clutter
              break;
            case "workflow-update":
              // Pass the raw data object directly for workflow-update events
              addLog(data);
              if (data && data.workflow_status) {
                const status = data.workflow_status.toLowerCase();
                setWorkflowStatus(status);
                if (
                  status === "waiting_for_approval" &&
                  data.approval_required === true &&
                  data.status === "paused"
                ) {
                  const nodeName = data.node_name || data.node_id || "Unknown";
                  addLog(`⏸️ Workflow is waiting for approval. Node: ${nodeName}`);
                }
              }
              break;
            case "workflow-completed":
              // Pass the raw data object for workflow-completed events
              addLog(data);
              addLog(`✅ Workflow completed successfully`);
              setWorkflowStatus("completed");
              setIsExecuting(false);
              setIsStreaming(false);
              break;
            case "workflow-failed":
              // Pass the raw data object for workflow-failed events
              addLog(data);
              addLog(`❌ Workflow failed`);
              setWorkflowStatus("failed");
              setIsExecuting(false);
              setIsStreaming(false);
              break;
            case "workflow-cancelled":
              // Pass the raw data object for workflow-cancelled events
              addLog(data);
              addLog(`⚠️ Workflow cancelled`);
              setWorkflowStatus("cancelled");
              setIsExecuting(false);
              setIsStreaming(false);
              break;
            case "error":
              const errorLogData = typeof data === "object" && data !== null ? JSON.stringify(data, null, 2) : data;
              addLog(`❌ Error: ${errorLogData}`);
              break;
            case "update":
            case "message":
            default:
              const defaultLogData = typeof data === "object" && data !== null ? JSON.stringify(data, null, 2) : data;
              if (defaultLogData && defaultLogData !== "{}") {
                addLog(defaultLogData);
              }
              break;
          }
        },
        onError: (error) => {
          console.error(`[${timestamp}] [setupSSEConnection] SSE connection error:`, error);
          setIsStreaming(false);
          addLog("❌ Error in execution stream connection");
        },
        onClose: (wasError) => {
          console.log(
            `[${timestamp}] [setupSSEConnection] SSE connection closed`,
            wasError ? "due to an error" : "",
          );
          setIsStreaming(false);
        },
      });

      // Store the client in the ref
      sseClientRef.current = sseClient;

      // Connect to the SSE stream
      sseClient.connect();

      console.log(`[${timestamp}] [setupSSEConnection] SSE connection setup complete`);
    },
    [addLog, setIsStreaming, setIsExecuting, setWorkflowStatus],
  );

  // Handle form submission - memoized to prevent unnecessary re-renders
  const handleSubmit = useCallback(async () => {
    const timestamp = new Date().toISOString().replace("T", " ").substring(0, 19);
    console.log(`[${timestamp}] [handleSubmit] ========== FORM SUBMISSION INITIATED ==========`);
    console.log(`[${timestamp}] [handleSubmit] Form is valid: ${isFormValid ? "YES" : "NO"}`);

    if (!isFormValid) {
      console.log(`[${timestamp}] [handleSubmit] Submission blocked - form is not valid`);
      return;
    }

    // Clear processed approvals for new execution
    console.log(`[${timestamp}] [handleSubmit] Clearing processed approvals for new execution`);
    setProcessedApprovals(new Set());

    // Clear previous logs before starting a new execution
    const { clearLogs } = useExecutionStore.getState();
    clearLogs();

    // Set workflow status to running
    setWorkflowStatus("running");

    try {
      console.log(`[${timestamp}] [handleSubmit] Executing workflow with values from the store...`);
      console.log(
        `[${timestamp}] [handleSubmit] Total field values: ${Object.keys(fieldValues).length}`,
      );

      // Log all field values being submitted
      Object.entries(fieldValues).forEach(([fieldId, value]) => {
        console.log(
          `[${timestamp}] [handleSubmit] Field value: ${fieldId} = ${JSON.stringify(value)}`,
        );
      });

      // Switch to logs tab before execution
      console.log(`[${timestamp}] [handleSubmit] Switching to logs tab`);
      setActiveTab("logs");

      // Use workflow_id from props or fallback to URL params for backward compatibility
      const workflow_id =
        workflowId ||
        (() => {
          const urlParams = new URLSearchParams(window.location.search);
          return urlParams.get("workflow_id");
        })();

      if (!workflow_id) {
        console.error(
          `[${timestamp}] [handleSubmit] No workflow_id found in props or URL parameters`,
        );
        addLog("❌ Error: No workflow ID found. Cannot execute workflow.");
        return;
      }

      console.log(`[${timestamp}] [handleSubmit] Using workflow_id: ${workflow_id}`);
      addLog(`Preparing to execute workflow with ID: ${workflow_id}`);

      // Prepare the user_dependent_fields array and user_payload_template array
      const user_dependent_fields: string[] = [];
      const user_payload_template: any[] = [];

      // Process field values for the execution payload
      missingFields.forEach((field) => {
        const fieldId = `${field.nodeId}_${field.name}`;
        let value = fieldValues[fieldId];

        if (value !== undefined) {
          // Add field name to dependent fields array
          user_dependent_fields.push(field.name);

          console.log(
            `[${timestamp}] [handleSubmit] Processing field ${field.name}, raw value:`,
            value,
          );
          console.log(
            `[${timestamp}] [handleSubmit] Value type: ${typeof value}, is object: ${typeof value === "object"}`,
          );

          // Check if the value is still wrapped and unwrap it here as a fallback
          if (typeof value === "object" && value !== null && "value" in value) {
            console.log(
              `[${timestamp}] [handleSubmit] DETECTED WRAPPED VALUE - unwrapping:`,
              value,
            );
            value = value.value;
            console.log(`[${timestamp}] [handleSubmit] Unwrapped value:`, value);
          }

          // Determine the processed value based on field type
          let processedValue;
          // Parse the value to the appropriate type based on field.inputType
          console.log("the processed value is printed here", value);
          processedValue = parseInputValue(value, field.inputType);
          console.log(
            `[${timestamp}] [handleSubmit] Converted value for field ${field.name} (${field.inputType}):`,
            processedValue,
            `(type: ${typeof processedValue})`
          );

          // Create the field entry as an array item with field name as key
          // This allows duplicate field names while maintaining clean structure
          const fieldEntry = {
            [field.name]: {
              value: processedValue,
              transition_id: field.nodeId,
            }
          };
          user_payload_template.push(fieldEntry);

          console.log(
            `[${timestamp}] [handleSubmit] Final processed field entry:`,
            fieldEntry,
          );
        }
      });

      console.log(
        `[${timestamp}] [handleSubmit] Prepared user_dependent_fields:`,
        user_dependent_fields,
      );
      console.log(
        `[${timestamp}] [handleSubmit] Prepared user_payload_template:`,
        user_payload_template,
      );

      // Log the structure of the payload template for debugging
      console.log(
        `[${timestamp}] [handleSubmit] Payload template structure analysis:`,
        Object.entries(user_payload_template).map(([key, value]) => ({
          field: key,
          hasTransitionId: typeof value === "object" && value !== null && "transition_id" in value,
          hasValue: typeof value === "object" && value !== null && "value" in value,
          type: typeof value,
          structure: typeof value === "object" && value !== null ? Object.keys(value) : "primitive",
        })),
      );

      // Create the execution payload
      const executionPayload: WorkflowExecuteWithUserInputsPayload = {
        workflow_id: workflow_id,
        approval: true, // Always set to true (boolean) for user-initiated executions
        payload: {
          user_dependent_fields: user_dependent_fields,
          user_payload_template: user_payload_template,
        },
      };

      console.log(
        `[${timestamp}] [handleSubmit] Sending execution request with payload:`,
        executionPayload,
      );
      addLog("Sending workflow execution request...");

      // Set executing state
      setIsExecuting(true);

      // Execute the workflow with the new API
      const result = await executeWorkflowWithUserInputs(executionPayload);

      if (result.success) {
        console.log(`[${timestamp}] [handleSubmit] Execution request successful:`, result);
        addLog(`✅ Workflow execution started successfully`);

        // Check if we have a correlation ID for streaming
        if (result.correlationId) {
          setCorrelationId(result.correlationId);
          addLog(`Streaming logs with correlation ID: ${result.correlationId}`);

          // Set up SSE connection directly instead of calling onExecute
          console.log(`[${timestamp}] [handleSubmit] Setting up SSE connection directly`);
          setupSSEConnection(result.correlationId);
        } else {
          console.error(
            `[${timestamp}] [handleSubmit] No correlationId returned from execution API`,
          );
          addLog("⚠️ No correlation ID returned. Cannot stream execution logs.");
          setIsExecuting(false);
        }
      } else {
        console.error(`[${timestamp}] [handleSubmit] Execution request failed:`, result);
        addLog(`❌ Error executing workflow: ${result.message || "Unknown error"}`);
        setIsExecuting(false);
      }
    } catch (error) {
      console.error(`[${timestamp}] [handleSubmit] ERROR executing workflow:`, error);
      addLog(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
      setIsExecuting(false);
    }
  }, [
    isFormValid,
    fieldValues,
    missingFields,
    setActiveTab,
    addLog,
    setCorrelationId,
    setIsExecuting,
    setWorkflowStatus,
    setupSSEConnection,
  ]);

  // Download logs - memoized to prevent unnecessary re-renders
  const handleDownloadLogs = useCallback(() => {
    if (!Array.isArray(logs) || logs.length === 0) {
      console.warn("No logs available to download");
      return;
    }

    // Convert logs to properly formatted strings
    const formattedLogs = logs.map((log) => {
      if (typeof log === 'object' && log !== null) {
        // For objects, convert to formatted JSON
        return JSON.stringify(log, null, 2);
      } else if (typeof log === 'string') {
        // For strings, return as-is
        return log;
      } else {
        // For any other type, convert to string
        return String(log);
      }
    });

    const logContent = formattedLogs.join("\n\n"); // Use double newlines to separate entries
    const blob = new Blob([logContent], { type: "text/plain" });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `workflow-execution-logs-${new Date().toISOString().replace(/:/g, "-")}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [logs]);

  // Only render the Dialog when isDialogOpen is true to avoid unnecessary renders
  if (!isDialogOpen) {
    return null;
  }

  console.log('ExecutionDialog rendering with isDialogOpen:', isDialogOpen);
  console.log('ExecutionDialog missingFields:', missingFields);
  console.log('ExecutionDialog activeTab:', activeTab);

  const modalContent = (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]"
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 9999,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}
      onClick={(e) => {
        // Close dialog when clicking on backdrop
        if (e.target === e.currentTarget) {
          onClose();
        }
      }}
    >
      <div
        className={`${isDarkMode ? 'bg-zinc-900' : 'bg-white'} rounded-xl p-6 w-[90vw] max-w-6xl h-[90vh] max-h-[800px] flex flex-col shadow-2xl`}
        style={{
          backgroundColor: isDarkMode ? '#18181b' : '#ffffff',
          position: 'relative',
          zIndex: 10000
        }}
      >

        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className={`${isDarkMode ? 'text-white' : 'text-gray-900'} text-xl font-bold`}>Workflow Execution</h2>
          <button
            className={`${isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-900'} transition-colors`}
            onClick={() => {
              // Prevent dialog from closing if the event originated from a toast interaction
              // Check if any toast elements are currently focused or being interacted with
              const activeElement = document.activeElement;
              const toastElements = document.querySelectorAll('[data-sonner-toast], [data-sonner-toaster]');
              const toastCloseButtons = document.querySelectorAll('[data-close-button="true"], [data-sonner-close-button]');

              // Check if the current focus or recent interaction is with toast elements
              const isToastRelated = Array.from(toastElements).some(toast =>
                toast.contains(activeElement) || toast === activeElement
              );

              const isToastCloseButton = Array.from(toastCloseButtons).some(button =>
                button.contains(activeElement) || button === activeElement
              );

              // Check if a toast interaction is currently active
              const isToastInteractionActive = (window as any)._toastInteractionActive === true;

              // Also check for recent toast interactions by looking at event target
              const recentToastInteraction = (() => {
                try {
                  // Check if there are any visible toasts
                  const visibleToasts = document.querySelectorAll('[data-sonner-toast]:not([data-dismissed="true"])');
                  return visibleToasts.length > 0;
                } catch (e) {
                  return false;
                }
              })();

              // Only close dialog if it's not related to toast interaction
              if (!isToastRelated && !isToastCloseButton && !isToastInteractionActive && !recentToastInteraction) {
                onClose();
              } else {
                console.log('Dialog close prevented due to toast interaction', {
                  isToastRelated,
                  isToastCloseButton,
                  isToastInteractionActive,
                  recentToastInteraction
                });
              }
            }}
          >
            <X size={24} />
          </button>
        </div>

        {/* Tab Navigation */}
        <div className={`flex ${isDarkMode ? 'bg-zinc-800' : 'bg-gray-100'} rounded-lg p-2 mb-6`}>
          <button
            onClick={() => setActiveTab('parameters')}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-semibold transition-colors ${
              activeTab === 'parameters'
                ? isDarkMode
                  ? 'bg-zinc-900 text-white'
                  : 'bg-white text-gray-900 shadow-sm'
                : isDarkMode
                  ? 'text-gray-400 hover:text-white'
                  : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Required parameter
          </button>
          <button
            onClick={() => logs.length > 0 && setActiveTab('logs')}
            disabled={logs.length === 0}
            className={`flex-1 py-3 px-4 rounded-md text-sm font-semibold transition-colors ${
              activeTab === 'logs'
                ? isDarkMode
                  ? 'bg-zinc-950 text-white'
                  : 'bg-white text-gray-900 shadow-sm'
                : logs.length === 0
                  ? isDarkMode
                    ? 'text-gray-600 cursor-not-allowed'
                    : 'text-gray-400 cursor-not-allowed'
                  : isDarkMode
                    ? 'text-gray-400 hover:text-white'
                    : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Execution logs
          </button>
        </div>

        {/* Content */}
        <div className={`flex-1 ${isDarkMode ? 'bg-zinc-900 border-zinc-700' : 'bg-gray-50 border-gray-200'} border rounded-lg overflow-hidden`}>
          {activeTab === 'parameters' && (
            <div className="flex flex-col h-full">
              {missingFields.length > 0 ? (
                <>
                  <div className={`box-border flex flex-row items-center p-5 gap-6 w-full h-[86px] ${isDarkMode ? 'bg-zinc-800 border-[#4B4B4D]' : 'bg-gray-100 border-gray-300'} border rounded-lg m-6 mb-4`}>
                    <div className="flex flex-row items-center p-0 gap-4 w-auto h-[46px]">
                      <Info className={`w-[30px] h-[30px] ${isDarkMode ? 'text-white' : 'text-gray-700'}`} strokeWidth={1.5} />
                      <div className="flex flex-col items-start p-0 gap-2 w-auto h-[46px]">
                        <div className={`w-auto h-[22px] font-bold text-base leading-[140%] tracking-[-0.02em] ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                          Workflow Parameters
                        </div>
                        <div className={`w-auto h-4 font-normal text-xs leading-4 ${isDarkMode ? 'text-[#6F6F6F]' : 'text-gray-600'}`}>
                          Please review and edit the following parameters before executing the workflow. All values are editable.
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="flex-1 px-6 pb-6 overflow-y-auto">
                    <div className="space-y-6">
                    {missingFields.map((field) => {
                      const fieldId = `${field.nodeId}_${field.name}`;

                      return (
                        <div key={fieldId} className="space-y-2">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <label className={`${isDarkMode ? 'text-white' : 'text-gray-900'} text-sm font-medium`}>
                                {field.nodeName || "Unknown Node"}: {field.displayName || field.name || "Unnamed Field"}
                              </label>
                              {field.info && (
                                <div className="relative group">
                                  <HelpCircle className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} cursor-help`} size={14} />
                                  <div className="absolute left-1/2 transform -translate-x-1/2 top-6 z-50 invisible group-hover:visible">
                                    {/* Arrow pointing up */}
                                    <div className={`absolute left-1/2 transform -translate-x-1/2 -top-1 border-l-[6px] border-r-[6px] border-b-[6px] border-l-transparent border-r-transparent ${isDarkMode ? 'border-b-[#2E2E2E]' : 'border-b-gray-800'}`}></div>
                                    {/* Tooltip content */}
                                    <div
                                      className={`${isDarkMode ? 'bg-[#2E2E2E] text-white' : 'bg-gray-800 text-white'} rounded-[4px] px-3 py-2 w-80 min-w-[200px] font-normal text-[10px] leading-4 whitespace-normal`}
                                      style={{
                                        filter: 'drop-shadow(0px 0px 4px rgba(0, 0, 0, 0.1))'
                                      }}
                                    >
                                      {field.info}
                                    </div>
                                  </div>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center gap-2 text-xs">
                              <span className={`${isDarkMode ? 'text-white' : 'text-gray-900'}`}>Type:</span>
                              <div className={`px-2.5 py-[5px] ${isDarkMode ? 'bg-zinc-800' : 'bg-gray-200'} rounded inline-flex justify-center items-center gap-2.5`}>
                                <span className={`justify-center ${isDarkMode ? 'text-white' : 'text-gray-900'} text-xs font-medium`}>{field.inputType}</span>
                              </div>
                              {/* <div className="px-2.5 py-[5px] bg-zinc-800 rounded inline-flex justify-center items-center gap-2.5">
                                <span className="justify-center text-White text-xs font-medium">{isRequired ? 'Required' : 'Optional'}</span>
                              </div>
                              <div className="px-2.5 py-[5px] bg-zinc-800 rounded inline-flex justify-center items-center gap-2.5">
                                {field.directly_connected_to_start && (
                                  <span className="justify-center text-White text-xs font-medium">Connected to start</span>
                                )}
                              </div> */}
                            </div>
                          </div>

                          {field.inputType === "boolean" ? (
                            <div className="flex items-center space-x-2">
                              <input
                                id={fieldId}
                                type="checkbox"
                                checked={fieldValues[fieldId] === true}
                                onChange={(e) => {
                                  // For boolean fields, ensure we're passing a proper boolean value
                                  const boolValue = e.target.checked;
                                  console.log(
                                    `Boolean field ${fieldId} changed to: ${boolValue}`,
                                  );
                                  handleFieldChange(fieldId, boolValue, field.inputType);
                                }}
                                className={`h-4 w-4 rounded ${isDarkMode ? 'border-zinc-600 bg-zinc-800' : 'border-gray-300 bg-white'} text-purple-600 focus:ring-purple-500`}
                              />
                              <label htmlFor={fieldId} className={`text-sm ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {field.displayName}
                              </label>
                            </div>
                          ) : field.inputType === "object" ||
                            field.inputType === "dict" ||
                            field.inputType === "json" ? (
                            // Simple JSON input for object fields
                            <div className="space-y-2">
                              <div className="mb-2 flex items-center gap-2">
                                <Code className="h-4 w-4 text-blue-400" />
                                <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} text-xs`}>
                                  Enter JSON object (e.g., {`{"key": "value", "number": 123}`})
                                </p>
                              </div>
                              <textarea
                                id={fieldId}
                                value={(() => {
                                  const currentValue = fieldValues[fieldId];
                                  // Handle wrapped values
                                  let unwrappedValue = currentValue;
                                  if (
                                    typeof currentValue === "object" &&
                                    currentValue !== null &&
                                    "value" in currentValue
                                  ) {
                                    unwrappedValue = currentValue.value;
                                  }

                                  // Convert object to JSON string for display
                                  if (
                                    typeof unwrappedValue === "object" &&
                                    unwrappedValue !== null
                                  ) {
                                    try {
                                      return JSON.stringify(unwrappedValue, null, 2);
                                    } catch (e) {
                                      return "{}";
                                    }
                                  }
                                  // If it's already a string, use it as-is
                                  return unwrappedValue || "{}";
                                })()}
                                onChange={(e) => {
                                  const jsonString = e.target.value;

                                  // Try to parse the JSON to validate it
                                  try {
                                    if (jsonString.trim() === "") {
                                      // Empty input - pass empty object
                                      handleFieldChange(fieldId, {}, field.inputType);
                                    } else {
                                      // Parse and pass the native object
                                      const parsedObject = JSON.parse(jsonString);
                                      handleFieldChange(fieldId, parsedObject, field.inputType);
                                    }
                                  } catch (e) {
                                    // Invalid JSON - still update the field value to show the user's input
                                    // but the validation will catch this error
                                    handleFieldChange(fieldId, jsonString, field.inputType);
                                  }
                                }}
                                placeholder={`Enter JSON object...`}
                                className={`w-full px-4 py-3 ${isDarkMode ? 'bg-zinc-800 border-zinc-600 text-white placeholder-gray-500' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'} border rounded-md focus:outline-none focus:border-[#AE00D0] focus:ring-2 focus:ring-purple-500/20 font-mono text-sm ${errors[fieldId] ? "border-red-500" : ""}`}
                                rows={4}
                              />
                            </div>
                          ) : field.inputType === "array" || field.inputType === "list" ? (
                            <textarea
                              id={fieldId}
                              value={fieldValues[fieldId] || ""}
                              onChange={(e) =>
                                handleFieldChange(fieldId, e.target.value, field.inputType)
                              }
                              placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                              className={`w-full px-4 py-3 ${isDarkMode ? 'bg-zinc-800 border-zinc-600 text-white placeholder-gray-500' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'} border rounded-md focus:outline-none focus:border-[#AE00D0] focus:ring-2 focus:ring-purple-500/20 font-mono text-sm ${errors[fieldId] ? "border-red-500" : ""}`}
                              rows={4}
                            />
                          ) : field.inputType === "number" ||
                            field.inputType === "int" ||
                            field.inputType === "float" ? (
                            <input
                              id={fieldId}
                              type="number"
                              value={fieldValues[fieldId] || ""}
                              onChange={(e) => {
                                const rawValue = e.target.value;
                                // Convert empty string to 0 for number fields, otherwise parse the value
                                const parsedValue = rawValue === "" ? 0 : parseInputValue(rawValue, field.inputType);
                                handleFieldChange(fieldId, parsedValue, field.inputType);
                              }}
                              placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                              className={`w-full px-4 py-3 ${isDarkMode ? 'bg-zinc-800 border-zinc-600 text-white placeholder-gray-500' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'} border rounded-md focus:outline-none focus:border-[#AE00D0] focus:ring-2 focus:ring-purple-500/20 ${errors[fieldId] ? "border-red-500" : ""}`}
                            />
                          ) : (
                            <input
                              id={fieldId}
                              value={fieldValues[fieldId] || ""}
                              onChange={(e) =>
                                handleFieldChange(fieldId, e.target.value, field.inputType)
                              }
                              placeholder={`Enter ${field.displayName || field.name || "value"}...`}
                              className={`w-full px-4 py-3 ${isDarkMode ? 'bg-zinc-800 border-zinc-600 text-white placeholder-gray-500' : 'bg-white border-gray-300 text-gray-900 placeholder-gray-400'} border rounded-md focus:outline-none focus:border-[#AE00D0] focus:ring-2 focus:ring-purple-500/20 ${errors[fieldId] ? "border-red-500" : ""}`}
                            />
                          )}

                          {errors[fieldId] && (
                            <p className="text-xs text-red-500">{errors[fieldId]}</p>
                          )}
                        </div>
                      );
                    })}
                    </div>
                  </div>

                  {/* <div className="mt-6 flex justify-end">
                    <button
                      type="button"
                      onClick={handleSubmit}
                      disabled={!isFormValid || isExecuting}
                      className="flex items-center gap-2 px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <Play size={16} />
                      Run workflow
                    </button>
                  </div> */}
                </>
              ) : (
                <div className="flex flex-grow items-center justify-center">
                  <div className="p-6 text-center">
                    <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-500" />
                    <h3 className={`mb-2 text-lg font-medium ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>No Parameters Found</h3>
                    <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-4 text-sm`}>
                      No workflow parameters were found. This is unusual - most workflows require
                      parameters.
                    </p>
                    <div className="flex justify-center gap-3">
                      <button
                        type="button"
                        onClick={() => {
                          // Close dialog to let user check the workflow
                          onClose();
                        }}
                        className={`px-6 py-2 ${isDarkMode ? 'bg-zinc-700 hover:bg-zinc-600' : 'bg-gray-200 hover:bg-gray-300'} ${isDarkMode ? 'text-white' : 'text-gray-900'} font-semibold rounded-md transition-colors`}
                      >
                        <FileText className="mr-1 h-4 w-4" />
                        Check Workflow
                      </button>
                      <button
                        type="button"
                        onClick={() => {
                          setActiveTab("logs");
                          handleSubmit();
                        }}
                        className="flex items-center gap-2 px-6 py-2 bg-purple-600 hover:bg-purple-700 text-white font-semibold rounded-md transition-colors"
                      >
                        <Play className="h-4 w-4" />
                        Execute Anyway
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'logs' && (
            <div className="h-full flex flex-col">
              {/* Logs Header */}
              <div className={`flex items-center justify-between p-5 border-b ${isDarkMode ? 'border-zinc-700' : 'border-gray-200'}`}>
                <h3 className={`${isDarkMode ? 'text-white' : 'text-gray-900'} font-semibold`}>Execution logs</h3>
                <div className="flex items-center gap-6">
                  <div className="flex items-center gap-2">
                    <span className={`${isDarkMode ? 'text-white' : 'text-gray-900'} text-sm font-medium`}>View raw results</span>
                    <div
                      className={`w-10 h-5 rounded-full relative cursor-pointer transition-colors ${
                        showRawResults
                          ? 'bg-[#AE00D0]/40'
                          : isDarkMode ? 'bg-zinc-700' : 'bg-gray-300'
                      }`}
                      onClick={() => setShowRawResults(!showRawResults)}
                    >
                      <div
                        className={`absolute w-4 h-4 top-0.5 rounded-full transition-all duration-200 ${
                          showRawResults
                            ? 'left-5 bg-[#AE00D0]'
                            : isDarkMode ? 'left-0.5 bg-zinc-500' : 'left-0.5 bg-gray-500'
                        }`}
                      ></div>
                    </div>
                  </div>
                  {logs.length > 0 && (
                    <button
                      onClick={handleDownloadLogs}
                      className={`flex items-center gap-2 px-3 py-2 ${isDarkMode ? 'bg-zinc-800 hover:bg-zinc-700 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-900'} rounded text-sm transition-colors`}
                    >
                      <Download size={16} />
                      Download logs
                    </button>
                  )}
                </div>
              </div>

              {/* Logs Content */}
              <div className="flex-1 flex flex-col min-h-0">
                {/* Scrollable Logs Area */}
                <div className="flex-1 p-5 space-y-4 overflow-y-auto min-h-0">
                  <LogDisplay logs={logs} showStreamingStatus={true} showRawResults={showRawResults} />

                  {/* Dialog Content Warnings */}
                  {logs.some((log) => typeof log === 'string' && log.includes("Missing 'Description'")) && (
                    <div className="border-t p-2">
                      <div className="mb-2 flex items-start gap-2 rounded-md border border-yellow-200 bg-yellow-50 p-2">
                        <AlertCircle className="mt-0.5 h-4 w-4 flex-shrink-0 text-yellow-500" />
                        <div className="flex-1">
                          <p className="text-xs font-medium text-yellow-800">
                            Dialog Content Warnings
                          </p>
                          <p className="text-xs text-yellow-700">
                            Missing descriptions for dialog content. This won't prevent execution
                            but should be fixed for better user experience.
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={addDefaultDescriptions}
                          className="rounded bg-yellow-100 px-2 py-1 text-xs text-yellow-800 hover:bg-yellow-200"
                        >
                          Add Default Descriptions
                        </button>
                      </div>
                    </div>
                  )}

                  {correlationId && (
                    <div className="text-gray-400 border-t border-zinc-700 p-2 text-xs">
                      <span className="font-medium">Correlation ID:</span> {correlationId}
                    </div>
                  )}
                </div>

                {/* Fixed Approval UI at Bottom - Always Visible */}
                {approvalNeeded && approvalDetails && (
                  <div className="flex-shrink-0 p-5 pt-0">
                    <div className={`flex flex-col items-start p-0 gap-6 w-full h-[86px] ${isDarkMode ? 'bg-[#1E1E1E]' : 'bg-orange-50'} rounded-lg`}>
                      <div className="flex flex-row items-center p-5 gap-6 w-full h-[86px] border border-[#F59E0B] rounded-lg">
                        <div className="flex flex-row justify-between items-center p-0 gap-4 w-full h-[46px]">
                          {/* Left side - Icon and text */}
                          <div className="flex flex-row items-start p-0 gap-2 w-auto h-[46px]">
                            {workflowStatus === "regenerating" ? (
                              <Loader className={`w-6 h-6 ${isDarkMode ? 'text-white' : 'text-gray-900'} animate-spin`} strokeWidth={1.5} />
                            ) : (
                              <Info className={`w-6 h-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`} strokeWidth={1.5} />
                            )}
                            <div className="w-auto h-[46px]">
                              <div className={`w-auto h-[22px] font-bold text-base leading-[140%] tracking-[-0.02em] ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                                {workflowStatus === "regenerating" ? "Regenerating..." : "Approval Required"}
                              </div>
                              <div className={`w-auto h-4 mt-2 font-normal text-xs leading-4 ${isDarkMode ? 'text-[#6F6F6F]' : 'text-gray-600'}`}>
                                {workflowStatus === "regenerating"
                                  ? `Regenerating content for node ${approvalDetails.nodeName}. New result will appear for approval.`
                                  : `Node ${approvalDetails.nodeName} requires your approval to continue execution`
                                }
                              </div>
                            </div>
                          </div>

                          {/* Right side - Buttons */}
                          <div className="flex flex-col items-end p-0 gap-6 w-[304px] h-10">
                            <div className="flex flex-row items-center p-0 gap-5 w-[304px] h-10">
                              <button
                                onClick={async () => {
                                  if (isApproveLoading) return; // Prevent multiple clicks

                                  setIsApproveLoading(true);
                                  try {
                                    // Import the API function
                                    const { sendApprovalDecision } = await import("@/lib/api");
                                    // Send approval decision with "approve" status
                                    const result = await sendApprovalDecision(approvalDetails.correlationId, "approve");

                                    if (result.success) {
                                      // Handle approval sent
                                      handleApprovalSent();
                                      // Add log entry
                                      addLog(`✅ Approval sent for node: ${approvalDetails.nodeName}`);
                                    } else {
                                      // Add error log entry
                                      addLog(`❌ Error sending approval: ${result.error}`);
                                    }
                                  } catch (error) {
                                    addLog(`❌ Error sending approval: ${error instanceof Error ? error.message : String(error)}`);
                                  } finally {
                                    setIsApproveLoading(false);
                                  }
                                }}
                                disabled={isApproveLoading || isRegenerateLoading || workflowStatus === "regenerating"}
                                className={`flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[142px] min-w-[80px] h-10 bg-[#AE00D0] rounded-md ${
                                  isApproveLoading || isRegenerateLoading || workflowStatus === "regenerating" ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                              >
                                {isApproveLoading ? (
                                  <Loader className="w-4 h-4 text-white animate-spin" strokeWidth={1.5} />
                                ) : (
                                  <BadgeCheck className="w-4 h-4 text-white" strokeWidth={1.5} />
                                )}
                                <span className="font-bold text-sm leading-5 text-white px-1">
                                  {isApproveLoading ? 'Approving...' : 'Approve'}
                                </span>
                              </button>
                              <button
                                onClick={async () => {
                                  if (isRegenerateLoading) return; // Prevent multiple clicks

                                  setIsRegenerateLoading(true);
                                  try {
                                    // Import the API function
                                    const { sendRegenerateRequest } = await import("@/lib/api");
                                    // Send regenerate request
                                    const result = await sendRegenerateRequest(approvalDetails.correlationId, approvalDetails.nodeId);

                                    if (result.success) {
                                      // Don't clear approval state - keep waiting for new result
                                      // The workflow will generate new content and pause again for approval
                                      setWorkflowStatus("regenerating");
                                      // Add log entry
                                      addLog(`🔄 Regenerate request sent for node: ${approvalDetails.nodeName}. Waiting for new result...`);
                                    } else {
                                      // Add error log entry
                                      addLog(`❌ Error sending regenerate request: ${result.error}`);
                                    }
                                  } catch (error) {
                                    addLog(`❌ Error sending regenerate request: ${error instanceof Error ? error.message : String(error)}`);
                                  } finally {
                                    setIsRegenerateLoading(false);
                                  }
                                }}
                                disabled={isApproveLoading || isRegenerateLoading || workflowStatus === "regenerating"}
                                className={`flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[142px] min-w-[80px] h-10 ${isDarkMode ? 'bg-[#2E2E2E] border-[#4B4B4D]' : 'bg-gray-200 border-gray-300'} border rounded-md ${
                                  isApproveLoading || isRegenerateLoading || workflowStatus === "regenerating" ? 'opacity-50 cursor-not-allowed' : ''
                                }`}
                              >
                                {isRegenerateLoading ? (
                                  <Loader className={`w-4 h-4 ${isDarkMode ? 'text-white' : 'text-gray-900'} animate-spin`} strokeWidth={1.5} />
                                ) : (
                                  <RotateCcw className={`w-4 h-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`} strokeWidth={1.5} />
                                )}
                                <span className={`font-bold text-sm leading-5 ${isDarkMode ? 'text-white' : 'text-gray-900'} px-1`}>
                                  {isRegenerateLoading ? 'Regenerating...' : 'Regenerate'}
                                </span>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex flex-row items-center p-0 gap-5 h-10 justify-center mt-6">
          <button
            className={`box-border flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[150px] min-w-[80px] h-10 ${isDarkMode ? 'bg-white border-[#EFF0F3] hover:bg-gray-50' : 'bg-gray-200 border-gray-300 hover:bg-gray-300'} border rounded-md transition-colors`}
            onClick={onClose}
          >
            <span className={`font-bold text-sm leading-5 ${isDarkMode ? 'text-[#313131]' : 'text-gray-900'} px-1`}>
              Cancel
            </span>
          </button>
          <button
            onClick={isExecuting ? (onStopExecution || (() => {})) : handleSubmit}
            disabled={!isExecuting && (!isFormValid || isExecuting)}
            className={`flex flex-row justify-center items-center py-2.5 px-3 gap-1 w-[150px] min-w-[80px] h-10 rounded-md transition-colors ${
              isExecuting
                ? "bg-[#991B1B] border border-[#7F1D1D] hover:bg-[#B91C1C]"
                : "bg-[#AE00D0] hover:bg-[#9A00B8] disabled:opacity-50 disabled:cursor-not-allowed"
            }`}
          >
            {isExecuting ? (
              <>
                <Square className="w-4 h-4 text-white" strokeWidth={1} />
                <span className="font-bold text-sm leading-5 text-white px-1 whitespace-nowrap">
                  Stop Execution
                </span>
              </>
            ) : (
              <>
                <Play className="w-4 h-4 text-white" strokeWidth={1} />
                <span className="font-bold text-sm leading-5 text-white px-1">
                  Run workflow
                </span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );

  // Render in a portal to ensure it appears above all other content
  return typeof window !== 'undefined' ? createPortal(modalContent, document.body) : null;
}
