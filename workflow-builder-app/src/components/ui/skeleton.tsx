import * as React from "react"
import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gray-200 dark:bg-gray-700",
        className
      )}
      {...props}
    />
  )
}

function SkeletonText({
  className,
  lines = 1,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & { lines?: number }) {
  return (
    <div className={cn("space-y-2", className)} {...props}>
      {Array.from({ length: lines }).map((_, i) => (
        <Skeleton
          key={i}
          className={cn(
            "h-4",
            i === lines - 1 && lines > 1 ? "w-3/4" : "w-full"
          )}
        />
      ))}
    </div>
  )
}

function SkeletonCard({
  className,
  showIcon = true,
  showTitle = true,
  showDescription = true,
  descriptionLines = 2,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  showIcon?: boolean
  showTitle?: boolean
  showDescription?: boolean
  descriptionLines?: number
}) {
  return (
    <div
      className={cn(
        "rounded-xl bg-gray-100 dark:bg-gray-800 p-4 space-y-3",
        className
      )}
      {...props}
    >
      <div className="flex items-start gap-2.5">
        {showIcon && (
          <div className="p-1 rounded bg-[#F4F4F5] dark:bg-[#2E2E2E] flex-shrink-0 mt-0.5">
            <Skeleton className="h-4 w-4" />
          </div>
        )}
        {showTitle && (
          <Skeleton className="h-4 flex-1" />
        )}
        <Skeleton className="h-4 w-4 flex-shrink-0" />
      </div>
      {showDescription && (
        <div className="space-y-1">
          {Array.from({ length: descriptionLines }).map((_, i) => (
            <Skeleton
              key={i}
              className={cn(
                "h-3",
                i === descriptionLines - 1 ? "w-2/3" : "w-full"
              )}
            />
          ))}
        </div>
      )}
    </div>
  )
}

function SkeletonMCPGroup({
  className,
  toolCount = 3,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  toolCount?: number
}) {
  return (
    <div
      className={cn(
        "rounded-lg bg-gray-100 dark:bg-gray-800 p-3 space-y-3",
        className
      )}
      {...props}
    >
      {/* MCP Group Header */}
      <div className="flex items-center gap-2.5 p-2 border-b border-gray-200/60 dark:border-gray-700/60 pb-2">
        <Skeleton className="h-8 w-8 rounded-md" />
        <Skeleton className="h-4 w-32" />
        <Skeleton className="h-3 w-3 ml-auto" />
      </div>
      
      {/* MCP Tools */}
      <div className="space-y-3">
        {Array.from({ length: toolCount }).map((_, i) => (
          <SkeletonCard
            key={i}
            className="bg-white dark:bg-gray-900"
            descriptionLines={Math.random() > 0.5 ? 1 : 2}
          />
        ))}
      </div>
    </div>
  )
}

function SkeletonCategoryHeader({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "flex items-center gap-2 p-4",
        className
      )}
      {...props}
    >
      <div className="p-1.5 rounded-md bg-gray-200 dark:bg-gray-700">
        <Skeleton className="h-6 w-6" />
      </div>
      <Skeleton className="h-5 w-24" />
      <Skeleton className="h-4 w-4 ml-auto" />
    </div>
  )
}

function SkeletonSidebarContent({
  className,
  categoryCount = 3,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  categoryCount?: number
}) {
  return (
    <div className={cn("space-y-1 px-4 py-4", className)} {...props}>
      {Array.from({ length: categoryCount }).map((_, i) => (
        <div
          key={i}
          className="overflow-hidden rounded-lg bg-gray-50 dark:bg-gray-900"
        >
          <SkeletonCategoryHeader />
          <div className="px-3 pb-4">
            <div className="space-y-4 pt-3">
              <SkeletonMCPGroup toolCount={Math.floor(Math.random() * 3) + 2} />
              {Math.random() > 0.5 && (
                <SkeletonMCPGroup toolCount={Math.floor(Math.random() * 2) + 1} />
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}

export { 
  Skeleton, 
  SkeletonText, 
  SkeletonCard, 
  SkeletonMCPGroup, 
  SkeletonCategoryHeader,
  SkeletonSidebarContent 
}