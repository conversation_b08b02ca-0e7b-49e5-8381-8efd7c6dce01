import React from "react";
import { InputDefinition } from "@/types";
import { ValidationWrapper } from "../ValidationWrapper";
import { Input } from "@/components/ui/input-credential";
import { ConnectedIndicator } from "@/components/ui/connected-indicator";
import { cn } from "@/lib/utils";
import { parseInputValue } from "@/utils/valueFormatting";

interface NumberInputProps {
  inputDef: InputDefinition;
  value: number | string;
  onChange: (name: string, value: any) => void;
  isDisabled?: boolean;
  isConnected?: boolean;
  nodeId?: string;
}

/**
 * Component for rendering number inputs (int and float)
 */
export function NumberInput({
  inputDef,
  value,
  onChange,
  isDisabled = false,
  isConnected = false,
  nodeId,
}: NumberInputProps) {
  const inputId = `config-${nodeId}-${inputDef.name}`;
  const isFloat = inputDef.input_type === "float";
  
  return (
    <ValidationWrapper inputDef={inputDef} value={value}>
      <div className="relative">
        <Input
          id={inputId}
          type="number"
          step={isFloat ? "any" : "1"}
          value={value ?? ""}
          onChange={(e) => {
            const rawValue = e.target.value;
            // Convert empty string to 0 for number fields, otherwise parse the value
            const parsedValue = rawValue === "" ? 0 : parseInputValue(rawValue, inputDef.input_type);
            onChange(inputDef.name, parsedValue);
          }}
          placeholder={inputDef.display_name}
          className={cn(
            "bg-background/50 mt-1 h-8 text-xs",
            isDisabled && "opacity-50"
          )}
          disabled={isDisabled}
        />
        {isDisabled && isConnected && <ConnectedIndicator />}
      </div>
    </ValidationWrapper>
  );
}
